name: premium_oracle
description: "Premium Oracle - Complete Mystical Consultation App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  go_router: ^14.6.2
  flutter_svg: ^2.0.12
  lottie: ^3.1.3
  cached_network_image: ^3.4.1
  
  # State Management & Architecture
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  equatable: ^2.0.5
  dartz: ^0.10.1
  get_it: ^8.0.2
  injectable: ^2.5.0
  
  # Network & API
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0
  connectivity_plus: ^6.1.0
  http: ^1.1.2
  flutter_dotenv: ^5.1.0
  
  # Local Storage & Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.3.3
  sqflite: ^2.4.1
  
  # Authentication & Security
  supabase_flutter: ^2.9.1
  crypto: ^3.0.6
  
  # Location & Weather
  geolocator: ^13.0.2
  weather: ^3.1.1
  geocoding: ^3.0.0
  
  # Date & Time
  intl: ^0.20.1
  timezone: ^0.9.4
  
  # Payments
  in_app_purchase: ^3.2.1
  
  # Notifications
  firebase_core: ^3.8.0
  firebase_messaging: ^15.1.4
  flutter_local_notifications: ^18.0.1
  
  # Utilities
  uuid: ^4.5.1
  logger: ^2.4.0
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Code Generation
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  riverpod_generator: ^2.6.2
  injectable_generator: ^2.6.2
  retrofit_generator: ^9.1.4
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1
  
  # Testing
  mocktail: ^1.0.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/tarot/
    - assets/runes/
    - assets/crystals/
    - assets/animations/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/birth_data.dart';
import '../../domain/entities/mystical_profile.dart';
import '../../domain/repositories/user_profile_repository.dart';
import '../../domain/services/mystical_calculator_service.dart';
import '../datasources/user_profile_remote_datasource.dart';
import '../models/birth_data_model.dart';
import '../models/mystical_profile_model.dart';

class UserProfileRepositoryImpl implements UserProfileRepository {
  final UserProfileRemoteDataSource remoteDataSource;
  final MysticalCalculatorService mysticalCalculatorService;
  final NetworkInfo networkInfo;

  UserProfileRepositoryImpl({
    required this.remoteDataSource,
    required this.mysticalCalculatorService,
    required this.networkInfo,
  });

  @override
  Future<MysticalProfile?> getUserProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = await remoteDataSource.getUserProfile(userId);
      return profileModel?.toEntity();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get user profile: ${e.toString()}');
    }
  }

  @override
  Future<MysticalProfile> createUserProfile(MysticalProfile profile) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = MysticalProfileModel.fromEntity(profile);
      final createdModel = await remoteDataSource.createUserProfile(profileModel);
      return createdModel.toEntity();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to create user profile: ${e.toString()}');
    }
  }

  @override
  Future<MysticalProfile> updateUserProfile(MysticalProfile profile) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = MysticalProfileModel.fromEntity(profile);
      final updatedModel = await remoteDataSource.updateUserProfile(profileModel);
      return updatedModel.toEntity();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to update user profile: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteUserProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await remoteDataSource.deleteUserProfile(userId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to delete user profile: ${e.toString()}');
    }
  }

  @override
  Future<BirthData?> getBirthData(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final birthDataModel = await remoteDataSource.getBirthData(userId);
      return birthDataModel?.toEntity();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get birth data: ${e.toString()}');
    }
  }

  @override
  Future<BirthData> saveBirthData(String userId, BirthData birthData) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final birthDataModel = BirthDataModel.fromEntity(birthData);
      final savedModel = await remoteDataSource.saveBirthData(userId, birthDataModel);
      return savedModel.toEntity();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to save birth data: ${e.toString()}');
    }
  }

  @override
  Future<BirthData> updateBirthData(String userId, BirthData birthData) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final birthDataModel = BirthDataModel.fromEntity(birthData);
      final updatedModel = await remoteDataSource.updateBirthData(userId, birthDataModel);
      return updatedModel.toEntity();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to update birth data: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteBirthData(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await remoteDataSource.deleteBirthData(userId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to delete birth data: ${e.toString()}');
    }
  }

  @override
  Future<AstrologicalProfile?> getAstrologicalProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = await remoteDataSource.getAstrologicalProfile(userId);
      return profileModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get astrological profile: ${e.toString()}');
    }
  }

  @override
  Future<AstrologicalProfile> saveAstrologicalProfile(String userId, AstrologicalProfile profile) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = AstrologicalProfileModel.fromEntity(profile);
      final savedModel = await remoteDataSource.saveAstrologicalProfile(userId, profileModel);
      return savedModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to save astrological profile: ${e.toString()}');
    }
  }

  @override
  Future<NumerologicalProfile?> getNumerologicalProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = await remoteDataSource.getNumerologicalProfile(userId);
      return profileModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get numerological profile: ${e.toString()}');
    }
  }

  @override
  Future<NumerologicalProfile> saveNumerologicalProfile(String userId, NumerologicalProfile profile) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = NumerologicalProfileModel.fromEntity(profile);
      final savedModel = await remoteDataSource.saveNumerologicalProfile(userId, profileModel);
      return savedModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to save numerological profile: ${e.toString()}');
    }
  }

  @override
  Future<ChineseProfile?> getChineseProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = await remoteDataSource.getChineseProfile(userId);
      return profileModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get Chinese profile: ${e.toString()}');
    }
  }

  @override
  Future<ChineseProfile> saveChineseProfile(String userId, ChineseProfile profile) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = ChineseProfileModel.fromEntity(profile);
      final savedModel = await remoteDataSource.saveChineseProfile(userId, profileModel);
      return savedModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to save Chinese profile: ${e.toString()}');
    }
  }

  @override
  Future<AyurvedicProfile?> getAyurvedicProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = await remoteDataSource.getAyurvedicProfile(userId);
      return profileModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get Ayurvedic profile: ${e.toString()}');
    }
  }

  @override
  Future<AyurvedicProfile> saveAyurvedicProfile(String userId, AyurvedicProfile profile) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModel = AyurvedicProfileModel.fromEntity(profile);
      final savedModel = await remoteDataSource.saveAyurvedicProfile(userId, profileModel);
      return savedModel;
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to save Ayurvedic profile: ${e.toString()}');
    }
  }

  @override
  Future<MysticalProfile> generateCompleteProfile({
    required String userId,
    required BirthData birthData,
    required String fullName,
    Map<String, dynamic>? ayurvedicConstitution,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      // Calculate all mystical profiles using the calculator service
      final astrologicalProfile = await mysticalCalculatorService.calculateAstrologicalProfile(birthData);
      final numerologicalProfile = mysticalCalculatorService.calculateNumerologicalProfile(birthData, fullName);
      final chineseProfile = mysticalCalculatorService.calculateChineseProfile(birthData.birthDate);
      final ayurvedicProfile = mysticalCalculatorService.calculateAyurvedicProfile(
        birthData, 
        ayurvedicConstitution ?? {},
      );

      // Create complete mystical profile
      final completeProfile = MysticalProfile(
        userId: userId,
        birthData: birthData,
        astrologicalProfile: astrologicalProfile,
        numerologicalProfile: numerologicalProfile,
        chineseProfile: chineseProfile,
        ayurvedicProfile: ayurvedicProfile,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save to database
      return await createUserProfile(completeProfile);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to generate complete profile: ${e.toString()}');
    }
  }

  @override
  Future<double> getProfileCompletionPercentage(String userId) async {
    try {
      final profile = await getUserProfile(userId);
      if (profile == null) return 0.0;
      
      return profile.completionPercentage;
    } catch (e) {
      throw ServerException(message: 'Failed to get profile completion percentage: ${e.toString()}');
    }
  }

  @override
  Future<List<String>> getMissingProfileSections(String userId) async {
    try {
      final profile = await getUserProfile(userId);
      if (profile == null) {
        return ['Birth Data', 'Astrological Profile', 'Numerological Profile', 'Chinese Astrology', 'Ayurvedic Profile'];
      }
      
      return profile.missingProfileSections;
    } catch (e) {
      throw ServerException(message: 'Failed to get missing profile sections: ${e.toString()}');
    }
  }

  @override
  Future<bool> isProfileComplete(String userId) async {
    try {
      final profile = await getUserProfile(userId);
      if (profile == null) return false;
      
      return profile.isComplete;
    } catch (e) {
      throw ServerException(message: 'Failed to check if profile is complete: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> exportProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      return await remoteDataSource.exportProfile(userId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to export profile: ${e.toString()}');
    }
  }

  @override
  Future<void> shareProfile(String userId, String shareMethod) async {
    try {
      // Export profile data
      final exportData = await exportProfile(userId);
      
      // TODO: Implement sharing logic based on shareMethod
      // This would typically involve platform-specific sharing implementations
      
      // For now, we'll just log the share action
      print('Sharing profile for user $userId via $shareMethod');
      print('Export data: $exportData');
    } catch (e) {
      throw ServerException(message: 'Failed to share profile: ${e.toString()}');
    }
  }

  @override
  Future<List<MysticalProfile>> getProfileHistory(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final profileModels = await remoteDataSource.getProfileHistory(userId);
      return profileModels.map((model) => model.toEntity()).toList();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to get profile history: ${e.toString()}');
    }
  }

  @override
  Future<MysticalProfile> getProfileAtDate(String userId, DateTime date) async {
    try {
      final profileHistory = await getProfileHistory(userId);
      
      // Find the profile closest to the specified date
      MysticalProfile? closestProfile;
      Duration? shortestDuration;
      
      for (final profile in profileHistory) {
        final duration = date.difference(profile.createdAt).abs();
        if (shortestDuration == null || duration < shortestDuration) {
          shortestDuration = duration;
          closestProfile = profile;
        }
      }
      
      if (closestProfile == null) {
        throw ServerException(message: 'No profile found for the specified date');
      }
      
      return closestProfile;
    } catch (e) {
      throw ServerException(message: 'Failed to get profile at date: ${e.toString()}');
    }
  }

  @override
  Future<void> backupProfile(String userId) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final exportData = await exportProfile(userId);
      await remoteDataSource.backupProfile(userId, exportData);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to backup profile: ${e.toString()}');
    }
  }

  @override
  Future<void> restoreProfile(String userId, Map<String, dynamic> backup) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      // Validate backup data
      if (!backup.containsKey('profile')) {
        throw ServerException(message: 'Invalid backup data: missing profile');
      }

      final profileData = backup['profile'] as Map<String, dynamic>;
      final profile = MysticalProfileModel.fromJson(profileData);
      
      // Restore the profile
      await remoteDataSource.updateUserProfile(profile);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: 'Failed to restore profile: ${e.toString()}');
    }
  }
}
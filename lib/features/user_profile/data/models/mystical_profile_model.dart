import '../../domain/entities/birth_data.dart';
import '../../domain/entities/mystical_profile.dart';
import 'birth_data_model.dart';

class MysticalProfileModel extends MysticalProfile {
  const MysticalProfileModel({
    required String userId,
    BirthData? birthData,
    AstrologicalProfile? astrologicalProfile,
    NumerologicalProfile? numerologicalProfile,
    ChineseProfile? chineseProfile,
    AyurvedicProfile? ayurvedicProfile,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) : super(
          userId: userId,
          birthData: birthData,
          astrologicalProfile: astrologicalProfile,
          numerologicalProfile: numerologicalProfile,
          chineseProfile: chineseProfile,
          ayurvedicProfile: ayurvedicProfile,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  factory MysticalProfileModel.fromJson(Map<String, dynamic> json) {
    return MysticalProfileModel(
      userId: json['user_id'],
      birthData: json['birth_data'] != null
          ? BirthDataModel.fromJson(json['birth_data'])
          : null,
      astrologicalProfile: json['astrological_profile'] != null
          ? AstrologicalProfileModel.fromJson(json['astrological_profile'])
          : null,
      numerologicalProfile: json['numerological_profile'] != null
          ? NumerologicalProfileModel.fromJson(json['numerological_profile'])
          : null,
      chineseProfile: json['chinese_profile'] != null
          ? ChineseProfileModel.fromJson(json['chinese_profile'])
          : null,
      ayurvedicProfile: json['ayurvedic_profile'] != null
          ? AyurvedicProfileModel.fromJson(json['ayurvedic_profile'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'birth_data': birthData != null
          ? BirthDataModel.fromEntity(birthData!).toJson()
          : null,
      'astrological_profile': astrologicalProfile != null
          ? AstrologicalProfileModel.fromEntity(astrologicalProfile!).toJson()
          : null,
      'numerological_profile': numerologicalProfile != null
          ? NumerologicalProfileModel.fromEntity(numerologicalProfile!).toJson()
          : null,
      'chinese_profile': chineseProfile != null
          ? ChineseProfileModel.fromEntity(chineseProfile!).toJson()
          : null,
      'ayurvedic_profile': ayurvedicProfile != null
          ? AyurvedicProfileModel.fromEntity(ayurvedicProfile!).toJson()
          : null,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory MysticalProfileModel.fromEntity(MysticalProfile entity) {
    return MysticalProfileModel(
      userId: entity.userId,
      birthData: entity.birthData,
      astrologicalProfile: entity.astrologicalProfile,
      numerologicalProfile: entity.numerologicalProfile,
      chineseProfile: entity.chineseProfile,
      ayurvedicProfile: entity.ayurvedicProfile,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  MysticalProfile toEntity() {
    return MysticalProfile(
      userId: userId,
      birthData: birthData,
      astrologicalProfile: astrologicalProfile,
      numerologicalProfile: numerologicalProfile,
      chineseProfile: chineseProfile,
      ayurvedicProfile: ayurvedicProfile,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

class AstrologicalProfileModel extends AstrologicalProfile {
  const AstrologicalProfileModel({
    required ZodiacSign sunSign,
    ZodiacSign? moonSign,
    ZodiacSign? risingSign,
    List<PlanetPosition>? planetPositions,
    List<HousePosition>? housePositions,
    List<AspectData>? aspects,
  }) : super(
          sunSign: sunSign,
          moonSign: moonSign,
          risingSign: risingSign,
          planetPositions: planetPositions,
          housePositions: housePositions,
          aspects: aspects,
        );

  factory AstrologicalProfileModel.fromJson(Map<String, dynamic> json) {
    return AstrologicalProfileModel(
      sunSign: ZodiacSign.values.firstWhere(
        (e) => e.toString().split('.').last == json['sun_sign'],
      ),
      moonSign: json['moon_sign'] != null
          ? ZodiacSign.values.firstWhere(
              (e) => e.toString().split('.').last == json['moon_sign'],
            )
          : null,
      risingSign: json['rising_sign'] != null
          ? ZodiacSign.values.firstWhere(
              (e) => e.toString().split('.').last == json['rising_sign'],
            )
          : null,
      planetPositions: json['planet_positions'] != null
          ? (json['planet_positions'] as List)
              .map((e) => PlanetPositionModel.fromJson(e))
              .toList()
          : null,
      housePositions: json['house_positions'] != null
          ? (json['house_positions'] as List)
              .map((e) => HousePositionModel.fromJson(e))
              .toList()
          : null,
      aspects: json['aspects'] != null
          ? (json['aspects'] as List)
              .map((e) => AspectDataModel.fromJson(e))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sun_sign': sunSign.toString().split('.').last,
      'moon_sign': moonSign?.toString().split('.').last,
      'rising_sign': risingSign?.toString().split('.').last,
      'planet_positions': planetPositions
          ?.map((e) => PlanetPositionModel.fromEntity(e).toJson())
          .toList(),
      'house_positions': housePositions
          ?.map((e) => HousePositionModel.fromEntity(e).toJson())
          .toList(),
      'aspects': aspects
          ?.map((e) => AspectDataModel.fromEntity(e).toJson())
          .toList(),
    };
  }

  factory AstrologicalProfileModel.fromEntity(AstrologicalProfile entity) {
    return AstrologicalProfileModel(
      sunSign: entity.sunSign,
      moonSign: entity.moonSign,
      risingSign: entity.risingSign,
      planetPositions: entity.planetPositions,
      housePositions: entity.housePositions,
      aspects: entity.aspects,
    );
  }
}

class NumerologicalProfileModel extends NumerologicalProfile {
  const NumerologicalProfileModel({
    required int lifePathNumber,
    required int destinyNumber,
    required int soulNumber,
    required int personalityNumber,
    required int birthdayNumber,
    required int karmaNumber,
    List<int>? luckyNumbers,
    PersonalYear? currentPersonalYear,
  }) : super(
          lifePathNumber: lifePathNumber,
          destinyNumber: destinyNumber,
          soulNumber: soulNumber,
          personalityNumber: personalityNumber,
          birthdayNumber: birthdayNumber,
          karmaNumber: karmaNumber,
          luckyNumbers: luckyNumbers,
          currentPersonalYear: currentPersonalYear,
        );

  factory NumerologicalProfileModel.fromJson(Map<String, dynamic> json) {
    return NumerologicalProfileModel(
      lifePathNumber: json['life_path_number'],
      destinyNumber: json['destiny_number'],
      soulNumber: json['soul_number'],
      personalityNumber: json['personality_number'],
      birthdayNumber: json['birthday_number'],
      karmaNumber: json['karma_number'],
      luckyNumbers: json['lucky_numbers'] != null
          ? List<int>.from(json['lucky_numbers'])
          : null,
      currentPersonalYear: json['current_personal_year'] != null
          ? PersonalYearModel.fromJson(json['current_personal_year'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'life_path_number': lifePathNumber,
      'destiny_number': destinyNumber,
      'soul_number': soulNumber,
      'personality_number': personalityNumber,
      'birthday_number': birthdayNumber,
      'karma_number': karmaNumber,
      'lucky_numbers': luckyNumbers,
      'current_personal_year': currentPersonalYear != null
          ? PersonalYearModel.fromEntity(currentPersonalYear!).toJson()
          : null,
    };
  }

  factory NumerologicalProfileModel.fromEntity(NumerologicalProfile entity) {
    return NumerologicalProfileModel(
      lifePathNumber: entity.lifePathNumber,
      destinyNumber: entity.destinyNumber,
      soulNumber: entity.soulNumber,
      personalityNumber: entity.personalityNumber,
      birthdayNumber: entity.birthdayNumber,
      karmaNumber: entity.karmaNumber,
      luckyNumbers: entity.luckyNumbers,
      currentPersonalYear: entity.currentPersonalYear,
    );
  }
}

class ChineseProfileModel extends ChineseProfile {
  const ChineseProfileModel({
    required ChineseZodiacAnimal zodiacAnimal,
    required ChineseElement element,
    required int yearInCycle,
    required YinYang polarity,
    List<ChineseZodiacAnimal>? compatibleAnimals,
    List<ChineseZodiacAnimal>? conflictingAnimals,
  }) : super(
          zodiacAnimal: zodiacAnimal,
          element: element,
          yearInCycle: yearInCycle,
          polarity: polarity,
          compatibleAnimals: compatibleAnimals,
          conflictingAnimals: conflictingAnimals,
        );

  factory ChineseProfileModel.fromJson(Map<String, dynamic> json) {
    return ChineseProfileModel(
      zodiacAnimal: ChineseZodiacAnimal.values.firstWhere(
        (e) => e.toString().split('.').last == json['zodiac_animal'],
      ),
      element: ChineseElement.values.firstWhere(
        (e) => e.toString().split('.').last == json['element'],
      ),
      yearInCycle: json['year_in_cycle'],
      polarity: YinYang.values.firstWhere(
        (e) => e.toString().split('.').last == json['polarity'],
      ),
      compatibleAnimals: json['compatible_animals'] != null
          ? (json['compatible_animals'] as List)
              .map((e) => ChineseZodiacAnimal.values.firstWhere(
                  (animal) => animal.toString().split('.').last == e))
              .toList()
          : null,
      conflictingAnimals: json['conflicting_animals'] != null
          ? (json['conflicting_animals'] as List)
              .map((e) => ChineseZodiacAnimal.values.firstWhere(
                  (animal) => animal.toString().split('.').last == e))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'zodiac_animal': zodiacAnimal.toString().split('.').last,
      'element': element.toString().split('.').last,
      'year_in_cycle': yearInCycle,
      'polarity': polarity.toString().split('.').last,
      'compatible_animals': compatibleAnimals
          ?.map((e) => e.toString().split('.').last)
          .toList(),
      'conflicting_animals': conflictingAnimals
          ?.map((e) => e.toString().split('.').last)
          .toList(),
    };
  }

  factory ChineseProfileModel.fromEntity(ChineseProfile entity) {
    return ChineseProfileModel(
      zodiacAnimal: entity.zodiacAnimal,
      element: entity.element,
      yearInCycle: entity.yearInCycle,
      polarity: entity.polarity,
      compatibleAnimals: entity.compatibleAnimals,
      conflictingAnimals: entity.conflictingAnimals,
    );
  }
}

class AyurvedicProfileModel extends AyurvedicProfile {
  const AyurvedicProfileModel({
    required Dosha dominantDosha,
    required Map<Dosha, double> doshaPercentages,
    List<String>? recommendedFoods,
    List<String>? avoidedFoods,
    List<String>? exerciseRecommendations,
  }) : super(
          dominantDosha: dominantDosha,
          doshaPercentages: doshaPercentages,
          recommendedFoods: recommendedFoods,
          avoidedFoods: avoidedFoods,
          exerciseRecommendations: exerciseRecommendations,
        );

  factory AyurvedicProfileModel.fromJson(Map<String, dynamic> json) {
    return AyurvedicProfileModel(
      dominantDosha: Dosha.values.firstWhere(
        (e) => e.toString().split('.').last == json['dominant_dosha'],
      ),
      doshaPercentages: Map<Dosha, double>.from(
        (json['dosha_percentages'] as Map).map(
          (key, value) => MapEntry(
            Dosha.values.firstWhere(
              (e) => e.toString().split('.').last == key,
            ),
            value.toDouble(),
          ),
        ),
      ),
      recommendedFoods: json['recommended_foods'] != null
          ? List<String>.from(json['recommended_foods'])
          : null,
      avoidedFoods: json['avoided_foods'] != null
          ? List<String>.from(json['avoided_foods'])
          : null,
      exerciseRecommendations: json['exercise_recommendations'] != null
          ? List<String>.from(json['exercise_recommendations'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dominant_dosha': dominantDosha.toString().split('.').last,
      'dosha_percentages': doshaPercentages.map(
        (key, value) => MapEntry(key.toString().split('.').last, value),
      ),
      'recommended_foods': recommendedFoods,
      'avoided_foods': avoidedFoods,
      'exercise_recommendations': exerciseRecommendations,
    };
  }

  factory AyurvedicProfileModel.fromEntity(AyurvedicProfile entity) {
    return AyurvedicProfileModel(
      dominantDosha: entity.dominantDosha,
      doshaPercentages: entity.doshaPercentages,
      recommendedFoods: entity.recommendedFoods,
      avoidedFoods: entity.avoidedFoods,
      exerciseRecommendations: entity.exerciseRecommendations,
    );
  }
}

// Supporting models
class PlanetPositionModel extends PlanetPosition {
  const PlanetPositionModel({
    required Planet planet,
    required ZodiacSign sign,
    required double degree,
    required int house,
  }) : super(
          planet: planet,
          sign: sign,
          degree: degree,
          house: house,
        );

  factory PlanetPositionModel.fromJson(Map<String, dynamic> json) {
    return PlanetPositionModel(
      planet: Planet.values.firstWhere(
        (e) => e.toString().split('.').last == json['planet'],
      ),
      sign: ZodiacSign.values.firstWhere(
        (e) => e.toString().split('.').last == json['sign'],
      ),
      degree: json['degree'].toDouble(),
      house: json['house'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'planet': planet.toString().split('.').last,
      'sign': sign.toString().split('.').last,
      'degree': degree,
      'house': house,
    };
  }

  factory PlanetPositionModel.fromEntity(PlanetPosition entity) {
    return PlanetPositionModel(
      planet: entity.planet,
      sign: entity.sign,
      degree: entity.degree,
      house: entity.house,
    );
  }
}

class HousePositionModel extends HousePosition {
  const HousePositionModel({
    required int houseNumber,
    required ZodiacSign sign,
    required double cuspDegree,
  }) : super(
          houseNumber: houseNumber,
          sign: sign,
          cuspDegree: cuspDegree,
        );

  factory HousePositionModel.fromJson(Map<String, dynamic> json) {
    return HousePositionModel(
      houseNumber: json['house_number'],
      sign: ZodiacSign.values.firstWhere(
        (e) => e.toString().split('.').last == json['sign'],
      ),
      cuspDegree: json['cusp_degree'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'house_number': houseNumber,
      'sign': sign.toString().split('.').last,
      'cusp_degree': cuspDegree,
    };
  }

  factory HousePositionModel.fromEntity(HousePosition entity) {
    return HousePositionModel(
      houseNumber: entity.houseNumber,
      sign: entity.sign,
      cuspDegree: entity.cuspDegree,
    );
  }
}

class AspectDataModel extends AspectData {
  const AspectDataModel({
    required Planet planet1,
    required Planet planet2,
    required AspectType aspectType,
    required double exactness,
  }) : super(
          planet1: planet1,
          planet2: planet2,
          aspectType: aspectType,
          exactness: exactness,
        );

  factory AspectDataModel.fromJson(Map<String, dynamic> json) {
    return AspectDataModel(
      planet1: Planet.values.firstWhere(
        (e) => e.toString().split('.').last == json['planet1'],
      ),
      planet2: Planet.values.firstWhere(
        (e) => e.toString().split('.').last == json['planet2'],
      ),
      aspectType: AspectType.values.firstWhere(
        (e) => e.toString().split('.').last == json['aspect_type'],
      ),
      exactness: json['exactness'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'planet1': planet1.toString().split('.').last,
      'planet2': planet2.toString().split('.').last,
      'aspect_type': aspectType.toString().split('.').last,
      'exactness': exactness,
    };
  }

  factory AspectDataModel.fromEntity(AspectData entity) {
    return AspectDataModel(
      planet1: entity.planet1,
      planet2: entity.planet2,
      aspectType: entity.aspectType,
      exactness: entity.exactness,
    );
  }
}

class PersonalYearModel extends PersonalYear {
  const PersonalYearModel({
    required int year,
    required int personalYearNumber,
    required String theme,
    required List<String> opportunities,
    required List<String> challenges,
  }) : super(
          year: year,
          personalYearNumber: personalYearNumber,
          theme: theme,
          opportunities: opportunities,
          challenges: challenges,
        );

  factory PersonalYearModel.fromJson(Map<String, dynamic> json) {
    return PersonalYearModel(
      year: json['year'],
      personalYearNumber: json['personal_year_number'],
      theme: json['theme'],
      opportunities: List<String>.from(json['opportunities']),
      challenges: List<String>.from(json['challenges']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'year': year,
      'personal_year_number': personalYearNumber,
      'theme': theme,
      'opportunities': opportunities,
      'challenges': challenges,
    };
  }

  factory PersonalYearModel.fromEntity(PersonalYear entity) {
    return PersonalYearModel(
      year: entity.year,
      personalYearNumber: entity.personalYearNumber,
      theme: entity.theme,
      opportunities: entity.opportunities,
      challenges: entity.challenges,
    );
  }
}
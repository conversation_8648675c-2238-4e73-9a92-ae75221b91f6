import '../../domain/entities/birth_data.dart';

class BirthDataModel extends BirthData {
  const BirthDataModel({
    required DateTime birthDate,
    TimeOfDay? birthTime,
    BirthLocationModel? birthLocation,
    bool hasAccurateBirthTime = false,
    String? notes,
  }) : super(
          birthDate: birthDate,
          birthTime: birthTime,
          birthLocation: birthLocation,
          hasAccurateBirthTime: hasAccurateBirthTime,
          notes: notes,
        );

  factory BirthDataModel.fromJson(Map<String, dynamic> json) {
    return BirthDataModel(
      birthDate: DateTime.parse(json['birth_date']),
      birthTime: json['birth_time'] != null
          ? TimeOfDay(
              hour: json['birth_time']['hour'],
              minute: json['birth_time']['minute'],
            )
          : null,
      birthLocation: json['birth_location'] != null
          ? BirthLocationModel.fromJson(json['birth_location'])
          : null,
      hasAccurateBirthTime: json['has_accurate_birth_time'] ?? false,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'birth_date': birthDate.toIso8601String(),
      'birth_time': birthTime != null
          ? {
              'hour': birthTime!.hour,
              'minute': birthTime!.minute,
            }
          : null,
      'birth_location': birthLocation != null
          ? (birthLocation as BirthLocationModel).toJson()
          : null,
      'has_accurate_birth_time': hasAccurateBirthTime,
      'notes': notes,
    };
  }

  factory BirthDataModel.fromEntity(BirthData entity) {
    return BirthDataModel(
      birthDate: entity.birthDate,
      birthTime: entity.birthTime,
      birthLocation: entity.birthLocation != null
          ? BirthLocationModel.fromEntity(entity.birthLocation!)
          : null,
      hasAccurateBirthTime: entity.hasAccurateBirthTime,
      notes: entity.notes,
    );
  }

  BirthData toEntity() {
    return BirthData(
      birthDate: birthDate,
      birthTime: birthTime,
      birthLocation: birthLocation,
      hasAccurateBirthTime: hasAccurateBirthTime,
      notes: notes,
    );
  }
}

class BirthLocationModel extends BirthLocation {
  const BirthLocationModel({
    required String cityName,
    String? stateName,
    required String countryName,
    required double latitude,
    required double longitude,
    String? timezone,
  }) : super(
          cityName: cityName,
          stateName: stateName,
          countryName: countryName,
          latitude: latitude,
          longitude: longitude,
          timezone: timezone,
        );

  factory BirthLocationModel.fromJson(Map<String, dynamic> json) {
    return BirthLocationModel(
      cityName: json['city_name'],
      stateName: json['state_name'],
      countryName: json['country_name'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      timezone: json['timezone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'city_name': cityName,
      'state_name': stateName,
      'country_name': countryName,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
    };
  }

  factory BirthLocationModel.fromEntity(BirthLocation entity) {
    return BirthLocationModel(
      cityName: entity.cityName,
      stateName: entity.stateName,
      countryName: entity.countryName,
      latitude: entity.latitude,
      longitude: entity.longitude,
      timezone: entity.timezone,
    );
  }

  BirthLocation toEntity() {
    return BirthLocation(
      cityName: cityName,
      stateName: stateName,
      countryName: countryName,
      latitude: latitude,
      longitude: longitude,
      timezone: timezone,
    );
  }
}
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/birth_data_model.dart';
import '../models/mystical_profile_model.dart';

abstract class UserProfileRemoteDataSource {
  // Profile CRUD
  Future<MysticalProfileModel?> getUserProfile(String userId);
  Future<MysticalProfileModel> createUserProfile(MysticalProfileModel profile);
  Future<MysticalProfileModel> updateUserProfile(MysticalProfileModel profile);
  Future<void> deleteUserProfile(String userId);
  
  // Birth Data operations
  Future<BirthDataModel?> getBirthData(String userId);
  Future<BirthDataModel> saveBirthData(String userId, BirthDataModel birthData);
  Future<BirthDataModel> updateBirthData(String userId, BirthDataModel birthData);
  Future<void> deleteBirthData(String userId);
  
  // Individual profile sections
  Future<AstrologicalProfileModel?> getAstrologicalProfile(String userId);
  Future<AstrologicalProfileModel> saveAstrologicalProfile(String userId, AstrologicalProfileModel profile);
  
  Future<NumerologicalProfileModel?> getNumerologicalProfile(String userId);
  Future<NumerologicalProfileModel> saveNumerologicalProfile(String userId, NumerologicalProfileModel profile);
  
  Future<ChineseProfileModel?> getChineseProfile(String userId);
  Future<ChineseProfileModel> saveChineseProfile(String userId, ChineseProfileModel profile);
  
  Future<AyurvedicProfileModel?> getAyurvedicProfile(String userId);
  Future<AyurvedicProfileModel> saveAyurvedicProfile(String userId, AyurvedicProfileModel profile);
  
  // Profile history and export
  Future<List<MysticalProfileModel>> getProfileHistory(String userId);
  Future<Map<String, dynamic>> exportProfile(String userId);
  Future<void> backupProfile(String userId, Map<String, dynamic> backup);
  Future<Map<String, dynamic>?> getProfileBackup(String userId);
}

class UserProfileRemoteDataSourceImpl implements UserProfileRemoteDataSource {
  final SupabaseClient supabaseClient;

  UserProfileRemoteDataSourceImpl({required this.supabaseClient});

  static const String _profilesTable = 'user_profiles';
  static const String _birthDataTable = 'birth_data';
  static const String _astrologicalTable = 'astrological_profiles';
  static const String _numerologicalTable = 'numerological_profiles';
  static const String _chineseTable = 'chinese_profiles';
  static const String _ayurvedicTable = 'ayurvedic_profiles';
  static const String _profileHistoryTable = 'profile_history';
  static const String _profileBackupsTable = 'profile_backups';

  @override
  Future<MysticalProfileModel?> getUserProfile(String userId) async {
    try {
      final response = await supabaseClient
          .from(_profilesTable)
          .select('''
            *,
            birth_data:$_birthDataTable(*),
            astrological_profile:$_astrologicalTable(*),
            numerological_profile:$_numerologicalTable(*),
            chinese_profile:$_chineseTable(*),
            ayurvedic_profile:$_ayurvedicTable(*)
          ''')
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return MysticalProfileModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get user profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<MysticalProfileModel> createUserProfile(MysticalProfileModel profile) async {
    try {
      final profileData = profile.toJson();
      
      // Remove nested objects for main profile insertion
      final mainProfileData = Map<String, dynamic>.from(profileData);
      mainProfileData.remove('birth_data');
      mainProfileData.remove('astrological_profile');
      mainProfileData.remove('numerological_profile');
      mainProfileData.remove('chinese_profile');
      mainProfileData.remove('ayurvedic_profile');

      final response = await supabaseClient
          .from(_profilesTable)
          .insert(mainProfileData)
          .select()
          .single();

      // Insert nested data if exists
      if (profile.birthData != null) {
        await _insertBirthData(profile.userId, profile.birthData! as BirthDataModel);
      }

      if (profile.astrologicalProfile != null) {
        await _insertAstrologicalProfile(profile.userId, profile.astrologicalProfile! as AstrologicalProfileModel);
      }

      if (profile.numerologicalProfile != null) {
        await _insertNumerologicalProfile(profile.userId, profile.numerologicalProfile! as NumerologicalProfileModel);
      }

      if (profile.chineseProfile != null) {
        await _insertChineseProfile(profile.userId, profile.chineseProfile! as ChineseProfileModel);
      }

      if (profile.ayurvedicProfile != null) {
        await _insertAyurvedicProfile(profile.userId, profile.ayurvedicProfile! as AyurvedicProfileModel);
      }

      return await getUserProfile(profile.userId) ?? 
             MysticalProfileModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to create user profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<MysticalProfileModel> updateUserProfile(MysticalProfileModel profile) async {
    try {
      final profileData = profile.toJson();
      
      // Update main profile
      final mainProfileData = Map<String, dynamic>.from(profileData);
      mainProfileData.remove('birth_data');
      mainProfileData.remove('astrological_profile');
      mainProfileData.remove('numerological_profile');
      mainProfileData.remove('chinese_profile');
      mainProfileData.remove('ayurvedic_profile');
      mainProfileData['updated_at'] = DateTime.now().toIso8601String();

      await supabaseClient
          .from(_profilesTable)
          .update(mainProfileData)
          .eq('user_id', profile.userId);

      // Update or insert nested data
      if (profile.birthData != null) {
        await _upsertBirthData(profile.userId, profile.birthData! as BirthDataModel);
      }

      if (profile.astrologicalProfile != null) {
        await _upsertAstrologicalProfile(profile.userId, profile.astrologicalProfile! as AstrologicalProfileModel);
      }

      if (profile.numerologicalProfile != null) {
        await _upsertNumerologicalProfile(profile.userId, profile.numerologicalProfile! as NumerologicalProfileModel);
      }

      if (profile.chineseProfile != null) {
        await _upsertChineseProfile(profile.userId, profile.chineseProfile! as ChineseProfileModel);
      }

      if (profile.ayurvedicProfile != null) {
        await _upsertAyurvedicProfile(profile.userId, profile.ayurvedicProfile! as AyurvedicProfileModel);
      }

      return await getUserProfile(profile.userId) ?? profile;
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to update user profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> deleteUserProfile(String userId) async {
    try {
      // Delete in reverse order of dependencies
      await supabaseClient.from(_ayurvedicTable).delete().eq('user_id', userId);
      await supabaseClient.from(_chineseTable).delete().eq('user_id', userId);
      await supabaseClient.from(_numerologicalTable).delete().eq('user_id', userId);
      await supabaseClient.from(_astrologicalTable).delete().eq('user_id', userId);
      await supabaseClient.from(_birthDataTable).delete().eq('user_id', userId);
      await supabaseClient.from(_profilesTable).delete().eq('user_id', userId);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to delete user profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<BirthDataModel?> getBirthData(String userId) async {
    try {
      final response = await supabaseClient
          .from(_birthDataTable)
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return BirthDataModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get birth data: ${e.toString()}',
      );
    }
  }

  @override
  Future<BirthDataModel> saveBirthData(String userId, BirthDataModel birthData) async {
    try {
      return await _insertBirthData(userId, birthData);
    } catch (e) {
      throw ServerException(
        message: 'Failed to save birth data: ${e.toString()}',
      );
    }
  }

  @override
  Future<BirthDataModel> updateBirthData(String userId, BirthDataModel birthData) async {
    try {
      return await _upsertBirthData(userId, birthData);
    } catch (e) {
      throw ServerException(
        message: 'Failed to update birth data: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> deleteBirthData(String userId) async {
    try {
      await supabaseClient
          .from(_birthDataTable)
          .delete()
          .eq('user_id', userId);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to delete birth data: ${e.toString()}',
      );
    }
  }

  @override
  Future<AstrologicalProfileModel?> getAstrologicalProfile(String userId) async {
    try {
      final response = await supabaseClient
          .from(_astrologicalTable)
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return AstrologicalProfileModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get astrological profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<AstrologicalProfileModel> saveAstrologicalProfile(
      String userId, AstrologicalProfileModel profile) async {
    try {
      return await _insertAstrologicalProfile(userId, profile);
    } catch (e) {
      throw ServerException(
        message: 'Failed to save astrological profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<NumerologicalProfileModel?> getNumerologicalProfile(String userId) async {
    try {
      final response = await supabaseClient
          .from(_numerologicalTable)
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return NumerologicalProfileModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get numerological profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<NumerologicalProfileModel> saveNumerologicalProfile(
      String userId, NumerologicalProfileModel profile) async {
    try {
      return await _insertNumerologicalProfile(userId, profile);
    } catch (e) {
      throw ServerException(
        message: 'Failed to save numerological profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<ChineseProfileModel?> getChineseProfile(String userId) async {
    try {
      final response = await supabaseClient
          .from(_chineseTable)
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return ChineseProfileModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get Chinese profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<ChineseProfileModel> saveChineseProfile(
      String userId, ChineseProfileModel profile) async {
    try {
      return await _insertChineseProfile(userId, profile);
    } catch (e) {
      throw ServerException(
        message: 'Failed to save Chinese profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<AyurvedicProfileModel?> getAyurvedicProfile(String userId) async {
    try {
      final response = await supabaseClient
          .from(_ayurvedicTable)
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return AyurvedicProfileModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get Ayurvedic profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<AyurvedicProfileModel> saveAyurvedicProfile(
      String userId, AyurvedicProfileModel profile) async {
    try {
      return await _insertAyurvedicProfile(userId, profile);
    } catch (e) {
      throw ServerException(
        message: 'Failed to save Ayurvedic profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<MysticalProfileModel>> getProfileHistory(String userId) async {
    try {
      final response = await supabaseClient
          .from(_profileHistoryTable)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return response
          .map((json) => MysticalProfileModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get profile history: ${e.toString()}',
      );
    }
  }

  @override
  Future<Map<String, dynamic>> exportProfile(String userId) async {
    try {
      final profile = await getUserProfile(userId);
      if (profile == null) {
        throw ServerException(message: 'Profile not found for export');
      }

      return {
        'profile': profile.toJson(),
        'exported_at': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
    } catch (e) {
      throw ServerException(
        message: 'Failed to export profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> backupProfile(String userId, Map<String, dynamic> backup) async {
    try {
      final backupData = {
        'user_id': userId,
        'backup_data': backup,
        'created_at': DateTime.now().toIso8601String(),
      };

      await supabaseClient
          .from(_profileBackupsTable)
          .insert(backupData);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to backup profile: ${e.toString()}',
      );
    }
  }

  @override
  Future<Map<String, dynamic>?> getProfileBackup(String userId) async {
    try {
      final response = await supabaseClient
          .from(_profileBackupsTable)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(1)
          .maybeSingle();

      if (response == null) return null;

      return response['backup_data'] as Map<String, dynamic>;
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get profile backup: ${e.toString()}',
      );
    }
  }

  // Private helper methods
  Future<BirthDataModel> _insertBirthData(String userId, BirthDataModel birthData) async {
    final data = birthData.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_birthDataTable)
        .insert(data)
        .select()
        .single();

    return BirthDataModel.fromJson(response);
  }

  Future<BirthDataModel> _upsertBirthData(String userId, BirthDataModel birthData) async {
    final data = birthData.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_birthDataTable)
        .upsert(data)
        .select()
        .single();

    return BirthDataModel.fromJson(response);
  }

  Future<AstrologicalProfileModel> _insertAstrologicalProfile(
      String userId, AstrologicalProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_astrologicalTable)
        .insert(data)
        .select()
        .single();

    return AstrologicalProfileModel.fromJson(response);
  }

  Future<AstrologicalProfileModel> _upsertAstrologicalProfile(
      String userId, AstrologicalProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_astrologicalTable)
        .upsert(data)
        .select()
        .single();

    return AstrologicalProfileModel.fromJson(response);
  }

  Future<NumerologicalProfileModel> _insertNumerologicalProfile(
      String userId, NumerologicalProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_numerologicalTable)
        .insert(data)
        .select()
        .single();

    return NumerologicalProfileModel.fromJson(response);
  }

  Future<NumerologicalProfileModel> _upsertNumerologicalProfile(
      String userId, NumerologicalProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_numerologicalTable)
        .upsert(data)
        .select()
        .single();

    return NumerologicalProfileModel.fromJson(response);
  }

  Future<ChineseProfileModel> _insertChineseProfile(
      String userId, ChineseProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_chineseTable)
        .insert(data)
        .select()
        .single();

    return ChineseProfileModel.fromJson(response);
  }

  Future<ChineseProfileModel> _upsertChineseProfile(
      String userId, ChineseProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_chineseTable)
        .upsert(data)
        .select()
        .single();

    return ChineseProfileModel.fromJson(response);
  }

  Future<AyurvedicProfileModel> _insertAyurvedicProfile(
      String userId, AyurvedicProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_ayurvedicTable)
        .insert(data)
        .select()
        .single();

    return AyurvedicProfileModel.fromJson(response);
  }

  Future<AyurvedicProfileModel> _upsertAyurvedicProfile(
      String userId, AyurvedicProfileModel profile) async {
    final data = profile.toJson();
    data['user_id'] = userId;

    final response = await supabaseClient
        .from(_ayurvedicTable)
        .upsert(data)
        .select()
        .single();

    return AyurvedicProfileModel.fromJson(response);
  }
}
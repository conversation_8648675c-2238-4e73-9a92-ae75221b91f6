import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/design_system/design_system.dart';
import '../../domain/entities/birth_data.dart' as entities;
import '../widgets/birth_date_picker.dart';
import '../widgets/birth_time_picker.dart';
import '../widgets/birth_location_picker.dart';

class BirthDataFormPage extends ConsumerStatefulWidget {
  const BirthDataFormPage({super.key});

  @override
  ConsumerState<BirthDataFormPage> createState() => _BirthDataFormPageState();
}

class _BirthDataFormPageState extends ConsumerState<BirthDataFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  
  DateTime? _selectedDate;
  entities.TimeOfDay? _selectedTime;
  entities.BirthLocation? _selectedLocation;
  bool _hasAccurateBirthTime = false;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Padding(
            padding: AppSpacing.screenPadding,
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => context.pop(),
                        icon: const Icon(Icons.arrow_back),
                        color: Colors.white,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Birth Information',
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            Text(
                              'Provide your birth details for accurate readings',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // Progress indicator
                  LinearProgressIndicator(
                    value: _getProgressValue(),
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary500),
                  ),

                  const SizedBox(height: 8),

                  Text(
                    _getProgressText(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white70,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Form content
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          // Birth Date Section
                          _buildSectionCard(
                            title: 'Birth Date',
                            subtitle: 'When were you born?',
                            icon: Icons.calendar_today,
                            isCompleted: _selectedDate != null,
                            child: BirthDatePicker(
                              initialDate: _selectedDate,
                              onDateSelected: (date) {
                                setState(() {
                                  _selectedDate = date;
                                });
                              },
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Birth Time Section
                          _buildSectionCard(
                            title: 'Birth Time',
                            subtitle: 'What time were you born?',
                            icon: Icons.access_time,
                            isCompleted: _selectedTime != null,
                            isOptional: true,
                            child: Column(
                              children: [
                                BirthTimePicker(
                                  initialTime: _selectedTime,
                                  onTimeSelected: (time) {
                                    setState(() {
                                      _selectedTime = time;
                                    });
                                  },
                                ),
                                const SizedBox(height: 16),
                                CheckboxListTile(
                                  title: Text(
                                    'I have accurate birth time',
                                    style: Theme.of(context).textTheme.bodyMedium,
                                  ),
                                  subtitle: Text(
                                    'Check this if you know the exact time',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.white60,
                                    ),
                                  ),
                                  value: _hasAccurateBirthTime,
                                  onChanged: (value) {
                                    setState(() {
                                      _hasAccurateBirthTime = value ?? false;
                                    });
                                  },
                                  activeColor: AppColors.primary500,
                                  checkColor: Colors.white,
                                  contentPadding: EdgeInsets.zero,
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Birth Location Section
                          _buildSectionCard(
                            title: 'Birth Location',
                            subtitle: 'Where were you born?',
                            icon: Icons.location_on,
                            isCompleted: _selectedLocation != null,
                            isOptional: true,
                            child: BirthLocationPicker(
                              initialLocation: _selectedLocation,
                              onLocationSelected: (location) {
                                setState(() {
                                  _selectedLocation = location;
                                });
                              },
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Notes Section
                          _buildSectionCard(
                            title: 'Additional Notes',
                            subtitle: 'Any additional information about your birth',
                            icon: Icons.notes,
                            isCompleted: _notesController.text.isNotEmpty,
                            isOptional: true,
                            child: TextFormField(
                              controller: _notesController,
                              maxLines: 3,
                              decoration: const InputDecoration(
                                hintText: 'Enter any additional birth information...',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),

                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),

                  // Bottom buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => context.pop(),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: BorderSide(
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: _canProceed() ? _handleSave : null,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text(
                            'Save Birth Data',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Widget child,
    required bool isCompleted,
    bool isOptional = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCompleted 
              ? AppColors.success500.withValues(alpha: 0.5)
              : Colors.white.withValues(alpha: 0.2),
        ),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isCompleted 
                      ? AppColors.success500.withValues(alpha: 0.2)
                      : AppColors.primary500.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isCompleted ? Icons.check : icon,
                  color: isCompleted ? AppColors.success500 : AppColors.primary500,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (isOptional)
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Optional',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontSize: 10,
                                color: Colors.white70,
                              ),
                            ),
                          ),
                      ],
                    ),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  double _getProgressValue() {
    int completed = 0;
    int total = 4; // date, time, location, notes

    if (_selectedDate != null) completed++;
    if (_selectedTime != null) completed++;
    if (_selectedLocation != null) completed++;
    if (_notesController.text.isNotEmpty) completed++;

    return completed / total;
  }

  String _getProgressText() {
    final progress = (_getProgressValue() * 100).round();
    return '$progress% completed';
  }

  bool _canProceed() {
    return _selectedDate != null; // Only birth date is required
  }

  void _handleSave() {
    if (!_formKey.currentState!.validate() || !_canProceed()) return;

    final birthData = entities.BirthData(
      birthDate: _selectedDate!,
      birthTime: _selectedTime,
      birthLocation: _selectedLocation,
      hasAccurateBirthTime: _hasAccurateBirthTime,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
    );

    // TODO: Save birth data using provider
    context.pop(birthData);
  }
}
import '../entities/birth_data.dart';
import '../entities/mystical_profile.dart';

abstract class UserProfileRepository {
  // User Profile CRUD operations
  Future<MysticalProfile?> getUserProfile(String userId);
  Future<MysticalProfile> createUserProfile(MysticalProfile profile);
  Future<MysticalProfile> updateUserProfile(MysticalProfile profile);
  Future<void> deleteUserProfile(String userId);
  
  // Birth Data operations
  Future<BirthData?> getBirthData(String userId);
  Future<BirthData> saveBirthData(String userId, BirthData birthData);
  Future<BirthData> updateBirthData(String userId, BirthData birthData);
  Future<void> deleteBirthData(String userId);
  
  // Mystical Profile operations
  Future<AstrologicalProfile?> getAstrologicalProfile(String userId);
  Future<AstrologicalProfile> saveAstrologicalProfile(String userId, AstrologicalProfile profile);
  
  Future<NumerologicalProfile?> getNumerologicalProfile(String userId);
  Future<NumerologicalProfile> saveNumerologicalProfile(String userId, NumerologicalProfile profile);
  
  Future<ChineseProfile?> getChineseProfile(String userId);
  Future<ChineseProfile> saveChineseProfile(String userId, ChineseProfile profile);
  
  Future<AyurvedicProfile?> getAyurvedicProfile(String userId);
  Future<AyurvedicProfile> saveAyurvedicProfile(String userId, AyurvedicProfile profile);
  
  // Profile calculation and generation
  Future<MysticalProfile> generateCompleteProfile({
    required String userId,
    required BirthData birthData,
    required String fullName,
    Map<String, dynamic>? ayurvedicConstitution,
  });
  
  // Profile statistics and completion
  Future<double> getProfileCompletionPercentage(String userId);
  Future<List<String>> getMissingProfileSections(String userId);
  Future<bool> isProfileComplete(String userId);
  
  // Profile sharing and export
  Future<Map<String, dynamic>> exportProfile(String userId);
  Future<void> shareProfile(String userId, String shareMethod);
  
  // Profile history and updates
  Future<List<MysticalProfile>> getProfileHistory(String userId);
  Future<MysticalProfile> getProfileAtDate(String userId, DateTime date);
  
  // Bulk operations
  Future<void> backupProfile(String userId);
  Future<void> restoreProfile(String userId, Map<String, dynamic> backup);
}
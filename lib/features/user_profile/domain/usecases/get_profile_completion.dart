import '../repositories/user_profile_repository.dart';

class GetProfileCompletion {
  final UserProfileRepository repository;

  GetProfileCompletion(this.repository);

  Future<ProfileCompletionInfo> call(String userId) async {
    final completionPercentage = await repository.getProfileCompletionPercentage(userId);
    final missingSection = await repository.getMissingProfileSections(userId);
    final isComplete = await repository.isProfileComplete(userId);

    return ProfileCompletionInfo(
      completionPercentage: completionPercentage,
      missingSections: missingSection,
      isComplete: isComplete,
    );
  }
}

class ProfileCompletionInfo {
  final double completionPercentage;
  final List<String> missingSections;
  final bool isComplete;

  const ProfileCompletionInfo({
    required this.completionPercentage,
    required this.missingSections,
    required this.isComplete,
  });
}
import '../entities/birth_data.dart';
import '../entities/mystical_profile.dart';
import '../repositories/user_profile_repository.dart';

class GenerateCompleteProfile {
  final UserProfileRepository repository;

  GenerateCompleteProfile(this.repository);

  Future<MysticalProfile> call({
    required String userId,
    required BirthData birthData,
    required String fullName,
    Map<String, dynamic>? ayurvedicConstitution,
  }) async {
    return await repository.generateCompleteProfile(
      userId: userId,
      birthData: birthData,
      fullName: fullName,
      ayurvedicConstitution: ayurvedicConstitution,
    );
  }
}
import 'package:equatable/equatable.dart';
import 'birth_data.dart';

class MysticalProfile extends Equatable {
  final String userId;
  final BirthData? birthData;
  final AstrologicalProfile? astrologicalProfile;
  final NumerologicalProfile? numerologicalProfile;
  final ChineseProfile? chineseProfile;
  final AyurvedicProfile? ayurvedicProfile;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MysticalProfile({
    required this.userId,
    this.birthData,
    this.astrologicalProfile,
    this.numerologicalProfile,
    this.chineseProfile,
    this.ayurvedicProfile,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        userId,
        birthData,
        astrologicalProfile,
        numerologicalProfile,
        chineseProfile,
        ayurvedicProfile,
        createdAt,
        updatedAt,
      ];

  MysticalProfile copyWith({
    String? userId,
    BirthData? birthData,
    AstrologicalProfile? astrologicalProfile,
    NumerologicalProfile? numerologicalProfile,
    ChineseProfile? chineseProfile,
    AyurvedicProfile? ayurvedicProfile,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MysticalProfile(
      userId: userId ?? this.userId,
      birthData: birthData ?? this.birthData,
      astrologicalProfile: astrologicalProfile ?? this.astrologicalProfile,
      numerologicalProfile: numerologicalProfile ?? this.numerologicalProfile,
      chineseProfile: chineseProfile ?? this.chineseProfile,
      ayurvedicProfile: ayurvedicProfile ?? this.ayurvedicProfile,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get isComplete => 
      birthData?.isComplete == true &&
      astrologicalProfile != null &&
      numerologicalProfile != null;

  double get completionPercentage {
    int completed = 0;
    int total = 5;

    if (birthData != null) completed++;
    if (astrologicalProfile != null) completed++;
    if (numerologicalProfile != null) completed++;
    if (chineseProfile != null) completed++;
    if (ayurvedicProfile != null) completed++;

    return (completed / total) * 100;
  }

  List<String> get missingProfileSections {
    final missing = <String>[];
    
    if (birthData == null) missing.add('Birth Data');
    if (astrologicalProfile == null) missing.add('Astrological Profile');
    if (numerologicalProfile == null) missing.add('Numerological Profile');
    if (chineseProfile == null) missing.add('Chinese Astrology');
    if (ayurvedicProfile == null) missing.add('Ayurvedic Profile');
    
    return missing;
  }
}

class AstrologicalProfile extends Equatable {
  final ZodiacSign sunSign;
  final ZodiacSign? moonSign;
  final ZodiacSign? risingSign;
  final List<PlanetPosition>? planetPositions;
  final List<HousePosition>? housePositions;
  final List<AspectData>? aspects;

  const AstrologicalProfile({
    required this.sunSign,
    this.moonSign,
    this.risingSign,
    this.planetPositions,
    this.housePositions,
    this.aspects,
  });

  @override
  List<Object?> get props => [
        sunSign,
        moonSign,
        risingSign,
        planetPositions,
        housePositions,
        aspects,
      ];

  bool get isBasicProfileComplete => 
      moonSign != null && risingSign != null;

  bool get isFullProfileComplete =>
      isBasicProfileComplete &&
      planetPositions != null &&
      housePositions != null;
}

class NumerologicalProfile extends Equatable {
  final int lifePathNumber;
  final int destinyNumber;
  final int soulNumber;
  final int personalityNumber;
  final int birthdayNumber;
  final int karmaNumber;
  final List<int>? luckyNumbers;
  final PersonalYear? currentPersonalYear;

  const NumerologicalProfile({
    required this.lifePathNumber,
    required this.destinyNumber,
    required this.soulNumber,
    required this.personalityNumber,
    required this.birthdayNumber,
    required this.karmaNumber,
    this.luckyNumbers,
    this.currentPersonalYear,
  });

  @override
  List<Object?> get props => [
        lifePathNumber,
        destinyNumber,
        soulNumber,
        personalityNumber,
        birthdayNumber,
        karmaNumber,
        luckyNumbers,
        currentPersonalYear,
      ];

  bool get isMasterNumber => 
      [11, 22, 33].contains(lifePathNumber) ||
      [11, 22, 33].contains(destinyNumber);
}

class ChineseProfile extends Equatable {
  final ChineseZodiacAnimal zodiacAnimal;
  final ChineseElement element;
  final int yearInCycle;
  final YinYang polarity;
  final List<ChineseZodiacAnimal>? compatibleAnimals;
  final List<ChineseZodiacAnimal>? conflictingAnimals;

  const ChineseProfile({
    required this.zodiacAnimal,
    required this.element,
    required this.yearInCycle,
    required this.polarity,
    this.compatibleAnimals,
    this.conflictingAnimals,
  });

  @override
  List<Object?> get props => [
        zodiacAnimal,
        element,
        yearInCycle,
        polarity,
        compatibleAnimals,
        conflictingAnimals,
      ];
}

class AyurvedicProfile extends Equatable {
  final Dosha dominantDosha;
  final Map<Dosha, double> doshaPercentages;
  final List<String>? recommendedFoods;
  final List<String>? avoidedFoods;
  final List<String>? exerciseRecommendations;

  const AyurvedicProfile({
    required this.dominantDosha,
    required this.doshaPercentages,
    this.recommendedFoods,
    this.avoidedFoods,
    this.exerciseRecommendations,
  });

  @override
  List<Object?> get props => [
        dominantDosha,
        doshaPercentages,
        recommendedFoods,
        avoidedFoods,
        exerciseRecommendations,
      ];
}

// Supporting enums and classes
enum ZodiacSign {
  aries, taurus, gemini, cancer, leo, virgo,
  libra, scorpio, sagittarius, capricorn, aquarius, pisces
}

enum ChineseZodiacAnimal {
  rat, ox, tiger, rabbit, dragon, snake,
  horse, goat, monkey, rooster, dog, pig
}

enum ChineseElement {
  wood, fire, earth, metal, water
}

enum YinYang { yin, yang }

enum Dosha { vata, pitta, kapha }

class PlanetPosition extends Equatable {
  final Planet planet;
  final ZodiacSign sign;
  final double degree;
  final int house;

  const PlanetPosition({
    required this.planet,
    required this.sign,
    required this.degree,
    required this.house,
  });

  @override
  List<Object> get props => [planet, sign, degree, house];
}

class HousePosition extends Equatable {
  final int houseNumber;
  final ZodiacSign sign;
  final double cuspDegree;

  const HousePosition({
    required this.houseNumber,
    required this.sign,
    required this.cuspDegree,
  });

  @override
  List<Object> get props => [houseNumber, sign, cuspDegree];
}

class AspectData extends Equatable {
  final Planet planet1;
  final Planet planet2;
  final AspectType aspectType;
  final double exactness;

  const AspectData({
    required this.planet1,
    required this.planet2,
    required this.aspectType,
    required this.exactness,
  });

  @override
  List<Object> get props => [planet1, planet2, aspectType, exactness];
}

class PersonalYear extends Equatable {
  final int year;
  final int personalYearNumber;
  final String theme;
  final List<String> opportunities;
  final List<String> challenges;

  const PersonalYear({
    required this.year,
    required this.personalYearNumber,
    required this.theme,
    required this.opportunities,
    required this.challenges,
  });

  @override
  List<Object> get props => [year, personalYearNumber, theme, opportunities, challenges];
}

enum Planet {
  sun, moon, mercury, venus, mars, jupiter, saturn,
  uranus, neptune, pluto, northNode, southNode
}

enum AspectType {
  conjunction, opposition, trine, square, sextile,
  quincunx, semisextile, semisquare, sesquiquadrate
}

// Extension methods for better usability
extension ZodiacSignExtension on ZodiacSign {
  String get displayName {
    switch (this) {
      case ZodiacSign.aries: return 'Aries';
      case ZodiacSign.taurus: return 'Taurus';
      case ZodiacSign.gemini: return 'Gemini';
      case ZodiacSign.cancer: return 'Cancer';
      case ZodiacSign.leo: return 'Leo';
      case ZodiacSign.virgo: return 'Virgo';
      case ZodiacSign.libra: return 'Libra';
      case ZodiacSign.scorpio: return 'Scorpio';
      case ZodiacSign.sagittarius: return 'Sagittarius';
      case ZodiacSign.capricorn: return 'Capricorn';
      case ZodiacSign.aquarius: return 'Aquarius';
      case ZodiacSign.pisces: return 'Pisces';
    }
  }

  String get element {
    switch (this) {
      case ZodiacSign.aries:
      case ZodiacSign.leo:
      case ZodiacSign.sagittarius:
        return 'Fire';
      case ZodiacSign.taurus:
      case ZodiacSign.virgo:
      case ZodiacSign.capricorn:
        return 'Earth';
      case ZodiacSign.gemini:
      case ZodiacSign.libra:
      case ZodiacSign.aquarius:
        return 'Air';
      case ZodiacSign.cancer:
      case ZodiacSign.scorpio:
      case ZodiacSign.pisces:
        return 'Water';
    }
  }

  String get modality {
    switch (this) {
      case ZodiacSign.aries:
      case ZodiacSign.cancer:
      case ZodiacSign.libra:
      case ZodiacSign.capricorn:
        return 'Cardinal';
      case ZodiacSign.taurus:
      case ZodiacSign.leo:
      case ZodiacSign.scorpio:
      case ZodiacSign.aquarius:
        return 'Fixed';
      case ZodiacSign.gemini:
      case ZodiacSign.virgo:
      case ZodiacSign.sagittarius:
      case ZodiacSign.pisces:
        return 'Mutable';
    }
  }
}

extension ChineseZodiacAnimalExtension on ChineseZodiacAnimal {
  String get displayName {
    switch (this) {
      case ChineseZodiacAnimal.rat: return 'Rat';
      case ChineseZodiacAnimal.ox: return 'Ox';
      case ChineseZodiacAnimal.tiger: return 'Tiger';
      case ChineseZodiacAnimal.rabbit: return 'Rabbit';
      case ChineseZodiacAnimal.dragon: return 'Dragon';
      case ChineseZodiacAnimal.snake: return 'Snake';
      case ChineseZodiacAnimal.horse: return 'Horse';
      case ChineseZodiacAnimal.goat: return 'Goat';
      case ChineseZodiacAnimal.monkey: return 'Monkey';
      case ChineseZodiacAnimal.rooster: return 'Rooster';
      case ChineseZodiacAnimal.dog: return 'Dog';
      case ChineseZodiacAnimal.pig: return 'Pig';
    }
  }
}

extension DoshaExtension on Dosha {
  String get displayName {
    switch (this) {
      case Dosha.vata: return 'Vata';
      case Dosha.pitta: return 'Pitta';
      case Dosha.kapha: return 'Kapha';
    }
  }

  String get description {
    switch (this) {
      case Dosha.vata: return 'Air & Space - Movement & Creativity';
      case Dosha.pitta: return 'Fire & Water - Transformation & Intelligence';
      case Dosha.kapha: return 'Earth & Water - Structure & Stability';
    }
  }
}
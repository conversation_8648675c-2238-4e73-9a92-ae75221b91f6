import 'package:equatable/equatable.dart';

class BirthData extends Equatable {
  final DateTime birthDate;
  final TimeOfDay? birthTime;
  final BirthLocation? birthLocation;
  final bool hasAccurateBirthTime;
  final String? notes;

  const BirthData({
    required this.birthDate,
    this.birthTime,
    this.birthLocation,
    this.hasAccurateBirthTime = false,
    this.notes,
  });

  @override
  List<Object?> get props => [
        birthDate,
        birthTime,
        birthLocation,
        hasAccurateBirthTime,
        notes,
      ];

  BirthData copyWith({
    DateTime? birthDate,
    TimeOfDay? birthTime,
    BirthLocation? birthLocation,
    bool? hasAccurateBirthTime,
    String? notes,
  }) {
    return BirthData(
      birthDate: birthDate ?? this.birthDate,
      birthTime: birthTime ?? this.birthTime,
      birthLocation: birthLocation ?? this.birthLocation,
      hasAccurateBirthTime: hasAccurateBirthTime ?? this.hasAccurateBirthTime,
      notes: notes ?? this.notes,
    );
  }

  // Helper methods
  int get ageInYears {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  bool get hasBirthTime => birthTime != null;
  bool get hasBirthLocation => birthLocation != null;
  bool get isComplete => hasBirthTime && hasBirthLocation;
}

class TimeOfDay extends Equatable {
  final int hour;
  final int minute;

  const TimeOfDay({
    required this.hour,
    required this.minute,
  });

  @override
  List<Object> get props => [hour, minute];

  factory TimeOfDay.fromDateTime(DateTime dateTime) {
    return TimeOfDay(
      hour: dateTime.hour,
      minute: dateTime.minute,
    );
  }

  DateTime toDateTime(DateTime date) {
    return DateTime(
      date.year,
      date.month,
      date.day,
      hour,
      minute,
    );
  }

  String toDisplayString() {
    final hourStr = hour.toString().padLeft(2, '0');
    final minuteStr = minute.toString().padLeft(2, '0');
    return '$hourStr:$minuteStr';
  }

  double get decimalHour => hour + (minute / 60.0);

  bool get isValid => hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
}

class BirthLocation extends Equatable {
  final String cityName;
  final String? stateName;
  final String countryName;
  final double latitude;
  final double longitude;
  final String? timezone;

  const BirthLocation({
    required this.cityName,
    this.stateName,
    required this.countryName,
    required this.latitude,
    required this.longitude,
    this.timezone,
  });

  @override
  List<Object?> get props => [
        cityName,
        stateName,
        countryName,
        latitude,
        longitude,
        timezone,
      ];

  String get fullLocationName {
    if (stateName != null && stateName!.isNotEmpty) {
      return '$cityName, $stateName, $countryName';
    }
    return '$cityName, $countryName';
  }

  String get coordinatesString {
    final latStr = latitude.toStringAsFixed(4);
    final lngStr = longitude.toStringAsFixed(4);
    return '$latStr, $lngStr';
  }

  BirthLocation copyWith({
    String? cityName,
    String? stateName,
    String? countryName,
    double? latitude,
    double? longitude,
    String? timezone,
  }) {
    return BirthLocation(
      cityName: cityName ?? this.cityName,
      stateName: stateName ?? this.stateName,
      countryName: countryName ?? this.countryName,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timezone: timezone ?? this.timezone,
    );
  }

  bool get isValid {
    return cityName.isNotEmpty &&
           countryName.isNotEmpty &&
           latitude >= -90 && latitude <= 90 &&
           longitude >= -180 && longitude <= 180;
  }
}
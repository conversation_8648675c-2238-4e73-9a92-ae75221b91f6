import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_result.dart';
import '../entities/user_entity.dart';

abstract class AuthRepository {
  // Authentication Methods
  Future<Either<Failure, AuthResult>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<Either<Failure, AuthResult>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  });

  Future<Either<Failure, AuthResult>> signInWithGoogle();

  Future<Either<Failure, AuthResult>> signInWithApple();

  Future<Either<Failure, void>> signOut();

  Future<Either<Failure, void>> resetPassword(String email);

  Future<Either<Failure, void>> confirmEmail(String token);

  Future<Either<Failure, void>> resendEmailConfirmation(String email);

  // User Session Management
  Future<Either<Failure, UserEntity?>> getCurrentUser();

  Stream<Either<Failure, AuthResult>> get authStateChanges;

  Future<Either<Failure, bool>> isUserLoggedIn();

  // Token Management
  Future<Either<Failure, String?>> getAccessToken();

  Future<Either<Failure, void>> refreshToken();

  // User Management
  Future<Either<Failure, UserEntity>> updateUserProfile({
    String? name,
    String? avatarUrl,
  });

  Future<Either<Failure, void>> deleteAccount();

  Future<Either<Failure, void>> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  // Session Validation
  Future<Either<Failure, bool>> validateSession();

  Future<Either<Failure, void>> invalidateAllSessions();
}
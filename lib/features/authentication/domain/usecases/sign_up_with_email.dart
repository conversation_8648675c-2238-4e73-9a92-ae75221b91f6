import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';

class SignUpWithEmail implements UseCase<AuthResult, SignUpWithEmailParams> {
  final AuthRepository repository;

  SignUpWithEmail(this.repository);

  @override
  Future<Either<Failure, AuthResult>> call(SignUpWithEmailParams params) async {
    if (!_isValidEmail(params.email)) {
      return const Left(ValidationFailure(message: 'Invalid email format'));
    }

    if (params.password.length < 6) {
      return const Left(ValidationFailure(message: 'Password must be at least 6 characters'));
    }

    if (params.name.trim().isEmpty) {
      return const Left(ValidationFailure(message: 'Name is required'));
    }

    if (params.name.trim().length < 2) {
      return const Left(ValidationFailure(message: 'Name must be at least 2 characters'));
    }

    return await repository.signUpWithEmailAndPassword(
      email: params.email.trim().toLowerCase(),
      password: params.password,
      name: params.name.trim(),
    );
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}

class SignUpWithEmailParams extends Equatable {
  final String email;
  final String password;
  final String name;

  const SignUpWithEmailParams({
    required this.email,
    required this.password,
    required this.name,
  });

  @override
  List<Object> get props => [email, password, name];
}
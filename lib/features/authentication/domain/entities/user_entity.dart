import 'package:equatable/equatable.dart';

class UserEntity extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime? lastSignInAt;
  final bool isEmailConfirmed;
  final AuthProvider provider;

  const UserEntity({
    required this.id,
    required this.email,
    this.name,
    this.avatarUrl,
    required this.createdAt,
    this.lastSignInAt,
    required this.isEmailConfirmed,
    required this.provider,
  });

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        avatarUrl,
        createdAt,
        lastSignInAt,
        isEmailConfirmed,
        provider,
      ];

  UserEntity copyWith({
    String? id,
    String? email,
    String? name,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    bool? isEmailConfirmed,
    AuthProvider? provider,
  }) {
    return UserEntity(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
      isEmailConfirmed: isEmailConfirmed ?? this.isEmailConfirmed,
      provider: provider ?? this.provider,
    );
  }
}

enum AuthProvider {
  email,
  google,
  apple,
}

extension AuthProviderExtension on AuthProvider {
  String get name {
    switch (this) {
      case AuthProvider.email:
        return 'email';
      case AuthProvider.google:
        return 'google';
      case AuthProvider.apple:
        return 'apple';
    }
  }

  static AuthProvider fromString(String provider) {
    switch (provider.toLowerCase()) {
      case 'google':
        return AuthProvider.google;
      case 'apple':
        return AuthProvider.apple;
      default:
        return AuthProvider.email;
    }
  }
}
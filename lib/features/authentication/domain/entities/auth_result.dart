import 'package:equatable/equatable.dart';
import 'user_entity.dart';

class AuthResult extends Equatable {
  final UserEntity? user;
  final String? accessToken;
  final String? refreshToken;
  final AuthStatus status;
  final String? message;

  const AuthResult({
    this.user,
    this.accessToken,
    this.refreshToken,
    required this.status,
    this.message,
  });

  const AuthResult.success({
    required UserEntity user,
    String? accessToken,
    String? refreshToken,
    String? message,
  }) : this(
          user: user,
          accessToken: accessToken,
          refreshToken: refreshToken,
          status: AuthStatus.success,
          message: message,
        );

  const AuthResult.failure({
    required String message,
  }) : this(
          status: AuthStatus.failure,
          message: message,
        );

  const AuthResult.loading()
      : this(
          status: AuthStatus.loading,
        );

  const AuthResult.unauthenticated()
      : this(
          status: AuthStatus.unauthenticated,
        );

  bool get isSuccess => status == AuthStatus.success;
  bool get isFailure => status == AuthStatus.failure;
  bool get isLoading => status == AuthStatus.loading;
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;

  @override
  List<Object?> get props => [
        user,
        accessToken,
        refreshToken,
        status,
        message,
      ];

  AuthResult copyWith({
    UserEntity? user,
    String? accessToken,
    String? refreshToken,
    AuthStatus? status,
    String? message,
  }) {
    return AuthResult(
      user: user ?? this.user,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      status: status ?? this.status,
      message: message ?? this.message,
    );
  }
}

enum AuthStatus {
  loading,
  success,
  failure,
  unauthenticated,
}
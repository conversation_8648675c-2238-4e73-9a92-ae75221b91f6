import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/auth_result.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/usecases/reset_password.dart';
import '../../domain/usecases/sign_in_with_email.dart';
import '../../domain/usecases/sign_in_with_google.dart';
import '../../domain/usecases/sign_out.dart';
import '../../domain/usecases/sign_up_with_email.dart';
import 'auth_providers.dart';

class AuthNotifier extends StateNotifier<AuthResult> {
  final SignInWithEmail _signInWithEmail;
  final SignUpWithEmail _signUpWithEmail;
  final SignInWithGoogle _signInWithGoogle;
  final SignOut _signOut;
  final ResetPassword _resetPassword;

  AuthNotifier({
    required SignInWithEmail signInWithEmail,
    required SignUpWithEmail signUpWithEmail,
    required SignInWithGoogle signInWithGoogle,
    required SignOut signOut,
    required ResetPassword resetPassword,
  })  : _signInWithEmail = signInWithEmail,
        _signUpWithEmail = signUpWithEmail,
        _signInWithGoogle = signInWithGoogle,
        _signOut = signOut,
        _resetPassword = resetPassword,
        super(const AuthResult.unauthenticated());

  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    state = const AuthResult.loading();
    
    final result = await _signInWithEmail(SignInWithEmailParams(
      email: email,
      password: password,
    ));

    result.fold(
      (failure) => state = AuthResult.failure(message: failure.toString()),
      (authResult) => state = authResult,
    );
  }

  Future<void> signUpWithEmail({
    required String email,
    required String password,
    required String name,
  }) async {
    state = const AuthResult.loading();
    
    final result = await _signUpWithEmail(SignUpWithEmailParams(
      email: email,
      password: password,
      name: name,
    ));

    result.fold(
      (failure) => state = AuthResult.failure(message: failure.toString()),
      (authResult) => state = authResult,
    );
  }

  Future<void> signInWithGoogle() async {
    state = const AuthResult.loading();
    
    final result = await _signInWithGoogle(NoParams());

    result.fold(
      (failure) => state = AuthResult.failure(message: failure.toString()),
      (authResult) => state = authResult,
    );
  }

  Future<void> signOut() async {
    final result = await _signOut(NoParams());

    result.fold(
      (failure) {
        // Even if sign out fails, we should set to unauthenticated
        state = const AuthResult.unauthenticated();
      },
      (_) => state = const AuthResult.unauthenticated(),
    );
  }

  Future<void> resetPassword(String email) async {
    await _resetPassword(ResetPasswordParams(email: email));
  }

  void updateAuthState(AuthResult newState) {
    state = newState;
  }

  UserEntity? get currentUser => state.user;
  bool get isAuthenticated => state.isSuccess && state.user != null;
  bool get isLoading => state.isLoading;
  bool get isUnauthenticated => state.isUnauthenticated;
  String? get errorMessage => state.message;
}

// Provider for AuthNotifier
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AuthResult>((ref) {
  return AuthNotifier(
    signInWithEmail: ref.read(signInWithEmailProvider),
    signUpWithEmail: ref.read(signUpWithEmailProvider),
    signInWithGoogle: ref.read(signInWithGoogleProvider),
    signOut: ref.read(signOutProvider),
    resetPassword: ref.read(resetPasswordProvider),
  );
});

// Convenience providers
final currentUserProvider = Provider<UserEntity?>((ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.user;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.isSuccess && authState.user != null;
});

final isLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.isLoading;
});
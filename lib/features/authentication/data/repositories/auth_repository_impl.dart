import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/auth_result.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_datasource.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, AuthResult>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signInWithEmailAndPassword(
          email: email,
          password: password,
        );
        
        final accessToken = await remoteDataSource.getAccessToken();
        
        return Right(AuthResult.success(
          user: user,
          accessToken: accessToken,
          message: 'Welcome back!',
        ));
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred during sign in.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signUpWithEmailAndPassword(
          email: email,
          password: password,
          name: name,
        );
        
        final accessToken = await remoteDataSource.getAccessToken();
        
        return Right(AuthResult.success(
          user: user,
          accessToken: accessToken,
          message: 'Account created successfully! Please check your email to confirm.',
        ));
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred during sign up.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> signInWithGoogle() async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signInWithGoogle();
        final accessToken = await remoteDataSource.getAccessToken();
        
        return Right(AuthResult.success(
          user: user,
          accessToken: accessToken,
          message: 'Welcome!',
        ));
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred during Google sign in.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> signInWithApple() async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signInWithApple();
        final accessToken = await remoteDataSource.getAccessToken();
        
        return Right(AuthResult.success(
          user: user,
          accessToken: accessToken,
          message: 'Welcome!',
        ));
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred during Apple sign in.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await remoteDataSource.signOut();
      return const Right(null);
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(message: e.message));
    } catch (e) {
      return Left(AuthenticationFailure(
        message: 'An unexpected error occurred during sign out.',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword(String email) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.resetPassword(email);
        return const Right(null);
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred while sending reset email.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> confirmEmail(String token) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.confirmEmail(token);
        return const Right(null);
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred during email confirmation.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> resendEmailConfirmation(String email) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.resendEmailConfirmation(email);
        return const Right(null);
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred while resending confirmation.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, UserEntity?>> getCurrentUser() async {
    try {
      final user = await remoteDataSource.getCurrentUser();
      return Right(user);
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(message: e.message));
    } catch (e) {
      return Left(AuthenticationFailure(
        message: 'An unexpected error occurred while getting current user.',
      ));
    }
  }

  @override
  Stream<Either<Failure, AuthResult>> get authStateChanges {
    return remoteDataSource.authStateChanges.map((user) {
      try {
        if (user == null) {
          return const Right(AuthResult.unauthenticated());
        } else {
          return Right(AuthResult.success(user: user));
        }
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An error occurred in auth state monitoring.',
        ));
      }
    });
  }

  @override
  Future<Either<Failure, bool>> isUserLoggedIn() async {
    try {
      final user = await remoteDataSource.getCurrentUser();
      return Right(user != null);
    } catch (e) {
      return const Right(false);
    }
  }

  @override
  Future<Either<Failure, String?>> getAccessToken() async {
    try {
      final token = await remoteDataSource.getAccessToken();
      return Right(token);
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(message: e.message));
    } catch (e) {
      return Left(AuthenticationFailure(
        message: 'An unexpected error occurred while getting access token.',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> refreshToken() async {
    try {
      await remoteDataSource.refreshToken();
      return const Right(null);
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(message: e.message));
    } catch (e) {
      return Left(AuthenticationFailure(
        message: 'An unexpected error occurred while refreshing token.',
      ));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateUserProfile({
    String? name,
    String? avatarUrl,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.updateUserProfile(
          name: name,
          avatarUrl: avatarUrl,
        );
        return Right(user);
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred while updating profile.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount() async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteAccount();
        return const Right(null);
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred while deleting account.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.changePassword(
          currentPassword: currentPassword,
          newPassword: newPassword,
        );
        return const Right(null);
      } on AuthenticationException catch (e) {
        return Left(AuthenticationFailure(message: e.message));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(AuthenticationFailure(
          message: 'An unexpected error occurred while changing password.',
        ));
      }
    } else {
      return const Left(NetworkFailure(
        message: 'No internet connection. Please check your network.',
      ));
    }
  }

  @override
  Future<Either<Failure, bool>> validateSession() async {
    try {
      final user = await remoteDataSource.getCurrentUser();
      return Right(user != null);
    } catch (e) {
      return const Right(false);
    }
  }

  @override
  Future<Either<Failure, void>> invalidateAllSessions() async {
    try {
      await remoteDataSource.signOut();
      return const Right(null);
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(message: e.message));
    } catch (e) {
      return Left(AuthenticationFailure(
        message: 'An unexpected error occurred while invalidating sessions.',
      ));
    }
  }
}
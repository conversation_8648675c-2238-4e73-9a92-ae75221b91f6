import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/user_entity.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends UserEntity {
  const UserModel({
    required super.id,
    required super.email,
    super.name,
    super.avatarUrl,
    required super.createdAt,
    super.lastSignInAt,
    required super.isEmailConfirmed,
    required super.provider,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  factory UserModel.fromSupabaseUser(Map<String, dynamic> user) {
    return UserModel(
      id: user['id'] as String,
      email: user['email'] as String? ?? '',
      name: user['user_metadata']?['name'] as String? ??
            user['user_metadata']?['display_name'] as String? ??
            user['user_metadata']?['full_name'] as String?,
      avatarUrl: user['user_metadata']?['avatar_url'] as String? ??
                user['user_metadata']?['picture'] as String?,
      createdAt: DateTime.parse(user['created_at'] as String),
      lastSignInAt: user['last_sign_in_at'] != null
          ? DateTime.parse(user['last_sign_in_at'] as String)
          : null,
      isEmailConfirmed: user['email_confirmed_at'] != null,
      provider: _getAuthProvider(user),
    );
  }

  static AuthProvider _getAuthProvider(Map<String, dynamic> user) {
    final appMetadata = user['app_metadata'] as Map<String, dynamic>?;
    final providers = appMetadata?['providers'] as List<dynamic>?;
    
    if (providers != null && providers.isNotEmpty) {
      final providerName = providers.first as String;
      return AuthProviderExtension.fromString(providerName);
    }
    
    return AuthProvider.email;
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    bool? isEmailConfirmed,
    AuthProvider? provider,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
      isEmailConfirmed: isEmailConfirmed ?? this.isEmailConfirmed,
      provider: provider ?? this.provider,
    );
  }

  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      id: entity.id,
      email: entity.email,
      name: entity.name,
      avatarUrl: entity.avatarUrl,
      createdAt: entity.createdAt,
      lastSignInAt: entity.lastSignInAt,
      isEmailConfirmed: entity.isEmailConfirmed,
      provider: entity.provider,
    );
  }
}
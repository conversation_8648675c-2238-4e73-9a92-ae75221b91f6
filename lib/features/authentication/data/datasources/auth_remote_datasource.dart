import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<UserModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  });

  Future<UserModel> signInWithGoogle();

  Future<UserModel> signInWithApple();

  Future<void> signOut();

  Future<void> resetPassword(String email);

  Future<void> confirmEmail(String token);

  Future<void> resendEmailConfirmation(String email);

  Future<UserModel?> getCurrentUser();

  Stream<UserModel?> get authStateChanges;

  Future<String?> getAccessToken();

  Future<void> refreshToken();

  Future<UserModel> updateUserProfile({
    String? name,
    String? avatarUrl,
  });

  Future<void> deleteAccount();

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  });
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient supabaseClient;

  AuthRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final response = await supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw const AuthenticationException(
          message: 'Failed to sign in. Please check your credentials.',
        );
      }

      return UserModel.fromSupabaseUser(response.user!.toJson());
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred during sign in.',
      );
    }
  }

  @override
  Future<UserModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final response = await supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: {
          'name': name,
          'display_name': name,
        },
      );

      if (response.user == null) {
        throw const AuthenticationException(
          message: 'Failed to create account. Please try again.',
        );
      }

      return UserModel.fromSupabaseUser(response.user!.toJson());
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred during sign up.',
      );
    }
  }

  @override
  Future<UserModel> signInWithGoogle() async {
    try {
      final response = await supabaseClient.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'com.premiumoracle.app://auth',
      );

      // Wait for auth state change
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw const AuthenticationException(
          message: 'Google sign in was cancelled or failed.',
        );
      }

      return UserModel.fromSupabaseUser(user.toJson());
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred during Google sign in.',
      );
    }
  }

  @override
  Future<UserModel> signInWithApple() async {
    try {
      final response = await supabaseClient.auth.signInWithOAuth(
        OAuthProvider.apple,
        redirectTo: 'com.premiumoracle.app://auth',
      );

      // Wait for auth state change
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw const AuthenticationException(
          message: 'Apple sign in was cancelled or failed.',
        );
      }

      return UserModel.fromSupabaseUser(user.toJson());
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred during Apple sign in.',
      );
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await supabaseClient.auth.signOut();
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw const AuthenticationException(
        message: 'An unexpected error occurred during sign out.',
      );
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      await supabaseClient.auth.resetPasswordForEmail(
        email,
        redirectTo: 'com.premiumoracle.app://reset-password',
      );
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while sending reset email.',
      );
    }
  }

  @override
  Future<void> confirmEmail(String token) async {
    try {
      await supabaseClient.auth.verifyOTP(
        type: OtpType.email,
        token: token,
        email: '', // Will be extracted from token
      );
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred during email confirmation.',
      );
    }
  }

  @override
  Future<void> resendEmailConfirmation(String email) async {
    try {
      await supabaseClient.auth.resend(
        type: OtpType.signup,
        email: email,
      );
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while resending confirmation.',
      );
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) return null;

      return UserModel.fromSupabaseUser(user.toJson());
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while getting current user.',
      );
    }
  }

  @override
  Stream<UserModel?> get authStateChanges {
    return supabaseClient.auth.onAuthStateChange.map((event) {
      final user = event.session?.user;
      if (user == null) return null;
      
      return UserModel.fromSupabaseUser(user.toJson());
    });
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      final session = supabaseClient.auth.currentSession;
      return session?.accessToken;
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while getting access token.',
      );
    }
  }

  @override
  Future<void> refreshToken() async {
    try {
      await supabaseClient.auth.refreshSession();
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while refreshing token.',
      );
    }
  }

  @override
  Future<UserModel> updateUserProfile({
    String? name,
    String? avatarUrl,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      
      if (name != null) {
        updateData['name'] = name;
        updateData['display_name'] = name;
      }
      
      if (avatarUrl != null) {
        updateData['avatar_url'] = avatarUrl;
      }

      final response = await supabaseClient.auth.updateUser(
        UserAttributes(data: updateData),
      );

      if (response.user == null) {
        throw const AuthenticationException(
          message: 'Failed to update user profile.',
        );
      }

      return UserModel.fromSupabaseUser(response.user!.toJson());
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while updating profile.',
      );
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      // Note: Supabase doesn't have a direct delete user method
      // This would typically be handled by a server-side function
      await supabaseClient.rpc('delete_user_account');
    } on PostgrestException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while deleting account.',
      );
    }
  }

  @override
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      await supabaseClient.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } on AuthException catch (e) {
      throw AuthenticationException(message: e.message);
    } catch (e) {
      throw AuthenticationException(
        message: 'An unexpected error occurred while changing password.',
      );
    }
  }
}
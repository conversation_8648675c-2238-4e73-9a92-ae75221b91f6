import '../entities/prediction_context.dart';

abstract class PredictionTemplateService {
  String getAstrologicalTemplate(AstrologicalContext context);
  String getNumerologicalTemplate(NumerologicalContext context);
  String getChineseTemplate(ChineseContext context);
  String getAyurvedicTemplate(AyurvedicContext context);
}

class PredictionTemplateServiceImpl implements PredictionTemplateService {
  @override
  String getAstrologicalTemplate(AstrologicalContext context) {
    final moonPhase = context.moonPhase.phase;
    final majorAspects = context.majorAspects;
    final retrogradeCount = context.retrogradeInfo.retrogradeList.length;
    
    String template = '''
ASTROLOGICAL FOCUS FOR TODAY:

Moon Phase Energy: ${moonPhase.meaning}
Current Moon Sign: ${context.moonPhase.moonSign.displayName} (${context.moonPhase.moonSign.element} element)

''';

    if (majorAspects.isNotEmpty) {
      template += '''
Major Planetary Aspects:
${majorAspects.map((aspect) => '- ${aspect.planet1.toString().split('.').last.toUpperCase()} ${aspect.aspectType.toString().split('.').last} ${aspect.planet2.toString().split('.').last.toUpperCase()}').join('\n')}

''';
    }

    if (retrogradeCount > 0) {
      template += '''
Retrograde Influence: ${retrogradeCount} planet(s) in retrograde
Retrograde planets: ${context.retrogradeInfo.retrogradeList.map((p) => p.toString().split('.').last.toUpperCase()).join(', ')}

''';
    }

    template += '''
Seasonal Theme: ${context.seasonalInfo.seasonalThemes.join(', ')}

Focus on how these cosmic energies specifically influence the user's personal astrological makeup and daily life. Provide practical guidance for navigating these influences.
''';

    return template;
  }

  @override
  String getNumerologicalTemplate(NumerologicalContext context) {
    return '''
NUMEROLOGICAL VIBRATIONS FOR TODAY:

Personal Day Number: ${context.personalDayNumber}
Universal Day Number: ${context.universalDayNumber}
Personal Month Number: ${context.personalMonthNumber}
Personal Year Number: ${context.personalYearNumber}

Current Life Phase: ${context.currentPhase.phaseName}
Phase Description: ${context.currentPhase.description}

Life Phase Opportunities:
${context.currentPhase.opportunities.map((opp) => '- $opp').join('\n')}

Life Phase Challenges:
${context.currentPhase.challenges.map((chal) => '- $chal').join('\n')}

Active Numerical Cycles: ${context.activeCycles.join(', ')}

Focus on how these numerical vibrations align with the user's life path and current circumstances. Provide specific guidance on how to work with these energies for personal growth and success.
''';
  }

  @override
  String getChineseTemplate(ChineseContext context) {
    return '''
CHINESE ASTROLOGY GUIDANCE FOR TODAY:

Day Animal: ${context.dayAnimal.displayName}
Day Element: ${context.dayElement.toString().split('.').last.toUpperCase()}
Day Polarity: ${context.dayPolarity.toString().split('.').last.toUpperCase()}
Day in 60-Day Cycle: ${context.dayInCycle}

Seasonal Information:
Season: ${context.seasonInfo.season.toString().split('.').last}
Seasonal Element: ${context.seasonInfo.seasonElement.toString().split('.').last.toUpperCase()}
Seasonal Advice: ${context.seasonInfo.seasonalAdvice.join(', ')}

Favorable Directions: ${context.favorableDirections.join(', ')}
Favorable Colors: ${context.favorableColors.join(', ')}

Focus on how the day's animal energy and elemental influences can guide decision-making, relationships, and activities. Emphasize harmony with natural cycles and the wisdom of Chinese metaphysics.
''';
  }

  @override
  String getAyurvedicTemplate(AyurvedicContext context) {
    return '''
AYURVEDIC WELLNESS GUIDANCE FOR TODAY:

Dominant Dosha of the Day: ${context.dominantDoshaOfDay.displayName}
Dosha Description: ${context.dominantDoshaOfDay.description}

Current Season: ${context.currentSeason.seasonName}
Seasonal Characteristics: ${context.currentSeason.characteristics.join(', ')}

RECOMMENDED FOR TODAY:
Foods to Favor: ${context.recommendedFoods.join(', ')}
Beneficial Activities: ${context.recommendedActivities.join(', ')}

AVOID TODAY:
Foods to Limit: ${context.avoidedFoods.join(', ')}

TIME-BASED RECOMMENDATIONS:
${context.timeRecommendations.timeSlots.entries.map((entry) => 
  '${entry.key}:00 - ${entry.value.join(', ')}'
).join('\n')}

Focus on achieving dosha balance through lifestyle choices, diet, and activities. Provide practical wellness advice that aligns with Ayurvedic principles and the user's constitution.
''';
  }
}

// Extension methods for better template customization
extension AstrologicalTemplateExtension on AstrologicalContext {
  String get dominantElement {
    final elements = <String, int>{};
    
    // Count elements from major aspects
    for (final aspect in majorAspects) {
      // This is simplified - in a real implementation, you'd calculate
      // the actual elements based on planet positions
    }
    
    return 'Fire'; // Placeholder
  }
  
  String get energyIntensity {
    final aspectCount = majorAspects.length;
    final retrogradeCount = retrogradeInfo.retrogradeList.length;
    
    if (aspectCount >= 3 && retrogradeCount >= 2) return 'Very High';
    if (aspectCount >= 2 || retrogradeCount >= 1) return 'High';
    if (aspectCount >= 1) return 'Moderate';
    return 'Low';
  }
}

extension NumerologicalTemplateExtension on NumerologicalContext {
  String get overallVibration {
    final sum = personalDayNumber + universalDayNumber;
    if (sum >= 15) return 'High Energy';
    if (sum >= 10) return 'Balanced';
    return 'Gentle';
  }
  
  bool get isMasterNumberDay {
    return [11, 22, 33].contains(personalDayNumber) ||
           [11, 22, 33].contains(universalDayNumber);
  }
}

extension ChineseTemplateExtension on ChineseContext {
  String get elementalBalance {
    // Simplified elemental interaction
    switch (dayElement) {
      case ChineseElement.wood:
        return 'Growth and creativity are enhanced';
      case ChineseElement.fire:
        return 'Passion and action are emphasized';
      case ChineseElement.earth:
        return 'Stability and grounding are important';
      case ChineseElement.metal:
        return 'Precision and clarity are favored';
      case ChineseElement.water:
        return 'Wisdom and intuition flow freely';
    }
  }
  
  String get yinYangGuidance {
    switch (dayPolarity) {
      case YinYang.yin:
        return 'A day for receptivity, introspection, and gentle action';
      case YinYang.yang:
        return 'A day for assertiveness, outward expression, and dynamic energy';
    }
  }
}

extension AyurvedicTemplateExtension on AyurvedicContext {
  String get dominantQuality {
    switch (dominantDoshaOfDay) {
      case Dosha.vata:
        return 'Movement, creativity, and change';
      case Dosha.pitta:
        return 'Transformation, focus, and intensity';
      case Dosha.kapha:
        return 'Stability, nurturing, and growth';
    }
  }
  
  String get balancingStrategy {
    switch (dominantDoshaOfDay) {
      case Dosha.vata:
        return 'Ground yourself with routine, warmth, and stable activities';
      case Dosha.pitta:
        return 'Cool down with moderate activities and avoid excess heat';
      case Dosha.kapha:
        return 'Energize with movement, stimulation, and lighter foods';
    }
  }
}
import '../entities/prediction.dart';\nimport '../entities/prediction_context.dart';\nimport '../../../user_profile/domain/entities/mystical_profile.dart';\nimport '../../../../core/services/ai_client.dart';\nimport 'prediction_template_service.dart';

import '../entities/prediction.dart';
import '../../../../core/services/ai_client.dart';
import '../entities/prediction_context.dart';

abstract class AIPredictionService {
  Future<DailyPrediction> generateAstrologicalPrediction({
    required PredictionContext context,
    required AstrologicalContext astrologicalData,
  });

  Future<DailyPrediction> generateNumerologicalPrediction({
    required PredictionContext context,
    required NumerologicalContext numerologicalData,
  });

  Future<DailyPrediction> generateChinesePrediction({
    required PredictionContext context,
    required ChineseContext chineseData,
  });

  Future<DailyPrediction> generateAyurvedicPrediction({
    required PredictionContext context,
    required AyurvedicContext ayurvedicData,
  });

  Future<PredictionSummary> generateDailySummary({
    required List<DailyPrediction> predictions,
    required PredictionContext context,
  });

  Future<String> enhancePredictionContent({
    required String baseContent,
    required PredictionType type,
    required Map<String, dynamic> enhancementData,
  });
}

class AIPredictionServiceImpl implements AIPredictionService {
  final AIClient aiClient;
  final PredictionTemplateService templateService;

  AIPredictionServiceImpl({
    required this.aiClient,
    required this.templateService,
  });

  @override
  Future<DailyPrediction> generateAstrologicalPrediction({
    required PredictionContext context,
    required AstrologicalContext astrologicalData,
  }) async {
    final template = templateService.getAstrologicalTemplate(astrologicalData);
    final prompt = _buildAstrologicalPrompt(context, astrologicalData, template);
    
    final aiResponse = await aiClient.generatePrediction(prompt);
    
    return DailyPrediction(
      id: _generateId(),
      userId: context.userId,
      date: context.date,
      type: PredictionType.astrological,
      title: aiResponse.title,
      content: aiResponse.content,
      summary: aiResponse.summary,
      keywords: aiResponse.keywords,
      mood: aiResponse.mood,
      energyLevel: aiResponse.energyLevel,
      luckyNumbers: aiResponse.luckyNumbers,
      luckyColors: aiResponse.luckyColors,
      advice: aiResponse.advice,
      warning: aiResponse.warning,
      metadata: {
        'moonPhase': astrologicalData.moonPhase.phase.toString(),
        'majorAspects': astrologicalData.majorAspects.map((a) => a.aspectType.toString()).toList(),
        'retrogradeCount': astrologicalData.retrogradeInfo.retrogradeList.length,
      },
      createdAt: DateTime.now(),
    );
  }

  @override
  Future<DailyPrediction> generateNumerologicalPrediction({
    required PredictionContext context,
    required NumerologicalContext numerologicalData,
  }) async {
    final template = templateService.getNumerologicalTemplate(numerologicalData);
    final prompt = _buildNumerologicalPrompt(context, numerologicalData, template);
    
    final aiResponse = await aiClient.generatePrediction(prompt);
    
    return DailyPrediction(
      id: _generateId(),
      userId: context.userId,
      date: context.date,
      type: PredictionType.numerological,
      title: aiResponse.title,
      content: aiResponse.content,
      summary: aiResponse.summary,
      keywords: aiResponse.keywords,
      mood: aiResponse.mood,
      energyLevel: aiResponse.energyLevel,
      luckyNumbers: aiResponse.luckyNumbers,
      luckyColors: aiResponse.luckyColors,
      advice: aiResponse.advice,
      warning: aiResponse.warning,
      metadata: {
        'personalDayNumber': numerologicalData.personalDayNumber,
        'universalDayNumber': numerologicalData.universalDayNumber,
        'personalYearNumber': numerologicalData.personalYearNumber,
        'currentPhase': numerologicalData.currentPhase.phaseName,
      },
      createdAt: DateTime.now(),
    );
  }

  @override
  Future<DailyPrediction> generateChinesePrediction({
    required PredictionContext context,
    required ChineseContext chineseData,
  }) async {
    final template = templateService.getChineseTemplate(chineseData);
    final prompt = _buildChinesePrompt(context, chineseData, template);
    
    final aiResponse = await aiClient.generatePrediction(prompt);
    
    return DailyPrediction(
      id: _generateId(),
      userId: context.userId,
      date: context.date,
      type: PredictionType.chinese,
      title: aiResponse.title,
      content: aiResponse.content,
      summary: aiResponse.summary,
      keywords: aiResponse.keywords,
      mood: aiResponse.mood,
      energyLevel: aiResponse.energyLevel,
      luckyNumbers: aiResponse.luckyNumbers,
      luckyColors: chineseData.favorableColors,
      advice: aiResponse.advice,
      warning: aiResponse.warning,
      metadata: {
        'dayAnimal': chineseData.dayAnimal.toString(),
        'dayElement': chineseData.dayElement.toString(),
        'dayPolarity': chineseData.dayPolarity.toString(),
        'favorableDirections': chineseData.favorableDirections,
      },
      createdAt: DateTime.now(),
    );
  }

  @override
  Future<DailyPrediction> generateAyurvedicPrediction({
    required PredictionContext context,
    required AyurvedicContext ayurvedicData,
  }) async {
    final template = templateService.getAyurvedicTemplate(ayurvedicData);
    final prompt = _buildAyurvedicPrompt(context, ayurvedicData, template);
    
    final aiResponse = await aiClient.generatePrediction(prompt);
    
    return DailyPrediction(
      id: _generateId(),
      userId: context.userId,
      date: context.date,
      type: PredictionType.ayurvedic,
      title: aiResponse.title,
      content: aiResponse.content,
      summary: aiResponse.summary,
      keywords: aiResponse.keywords,
      mood: aiResponse.mood,
      energyLevel: aiResponse.energyLevel,
      luckyNumbers: aiResponse.luckyNumbers,
      luckyColors: aiResponse.luckyColors,
      advice: aiResponse.advice,
      warning: aiResponse.warning,
      metadata: {
        'dominantDosha': ayurvedicData.dominantDoshaOfDay.toString(),
        'season': ayurvedicData.currentSeason.seasonName,
        'recommendedFoods': ayurvedicData.recommendedFoods,
        'avoidedFoods': ayurvedicData.avoidedFoods,
      },
      createdAt: DateTime.now(),
    );
  }

  @override
  Future<PredictionSummary> generateDailySummary({
    required List<DailyPrediction> predictions,
    required PredictionContext context,
  }) async {
    final prompt = _buildSummaryPrompt(predictions, context);
    final aiResponse = await aiClient.generateSummary(prompt);
    
    return PredictionSummary(
      overallMood: aiResponse.overallMood,
      overallEnergyLevel: aiResponse.overallEnergyLevel,
      dailyTheme: aiResponse.dailyTheme,
      topKeywords: aiResponse.topKeywords,
      mainAdvice: aiResponse.mainAdvice,
      primaryFocus: aiResponse.primaryFocus,
      avoidActions: aiResponse.avoidActions,
      favorableActions: aiResponse.favorableActions,
    );
  }

  @override
  Future<String> enhancePredictionContent({
    required String baseContent,
    required PredictionType type,
    required Map<String, dynamic> enhancementData,
  }) async {
    final prompt = _buildEnhancementPrompt(baseContent, type, enhancementData);
    final aiResponse = await aiClient.enhanceContent(prompt);
    return aiResponse;
  }

  // Private helper methods for building prompts
  String _buildAstrologicalPrompt(
    PredictionContext context,
    AstrologicalContext astrologicalData,
    String template,
  ) {
    final userProfile = context.userProfile;
    final birthData = userProfile.birthData;
    final astroProfile = userProfile.astrologicalProfile;

    return '''
Create a personalized astrological prediction for ${context.date.toString().split(' ')[0]}.

USER PROFILE:
- Sun Sign: ${astroProfile?.sunSign.displayName ?? 'Unknown'}
- Moon Sign: ${astroProfile?.moonSign?.displayName ?? 'Unknown'}
- Rising Sign: ${astroProfile?.risingSign?.displayName ?? 'Unknown'}
- Birth Date: ${birthData?.birthDate.toString().split(' ')[0] ?? 'Unknown'}

CURRENT ASTROLOGICAL CONDITIONS:
- Moon Phase: ${astrologicalData.moonPhase.phase.displayName} in ${astrologicalData.moonPhase.moonSign.displayName}
- Moon Meaning: ${astrologicalData.moonPhase.phase.meaning}
- Major Aspects: ${astrologicalData.majorAspects.map((a) => '${a.planet1.toString().split('.').last} ${a.aspectType.toString().split('.').last} ${a.planet2.toString().split('.').last}').join(', ')}
- Retrograde Planets: ${astrologicalData.retrogradeInfo.retrogradeList.map((p) => p.toString().split('.').last).join(', ')}
- Season: ${astrologicalData.seasonalInfo.season.toString().split('.').last}

TEMPLATE: $template

Generate a prediction that includes:
1. Title (max 60 characters)
2. Content (detailed prediction, 150-250 words)
3. Summary (one sentence, max 120 characters)
4. Keywords (3-5 relevant keywords)
5. Advice (actionable guidance, max 100 words)
6. Warning (potential challenges, max 80 words)
7. Lucky numbers (3-5 numbers)
8. Lucky colors (2-3 colors)
9. Energy level (1-10 scale)
10. Mood (excellent/good/neutral/challenging/difficult)

Make it personal, insightful, and actionable. Focus on how the current astrological energies specifically affect someone with this user's birth chart.
''';
  }

  String _buildNumerologicalPrompt(
    PredictionContext context,
    NumerologicalContext numerologicalData,
    String template,
  ) {
    final userProfile = context.userProfile;
    final numeroProfile = userProfile.numerologicalProfile;

    return '''
Create a personalized numerological prediction for ${context.date.toString().split(' ')[0]}.

USER PROFILE:
- Life Path Number: ${numeroProfile?.lifePathNumber ?? 'Unknown'}
- Destiny Number: ${numeroProfile?.destinyNumber ?? 'Unknown'}
- Soul Number: ${numeroProfile?.soulNumber ?? 'Unknown'}
- Personality Number: ${numeroProfile?.personalityNumber ?? 'Unknown'}

CURRENT NUMEROLOGICAL VIBRATIONS:
- Personal Day Number: ${numerologicalData.personalDayNumber}
- Universal Day Number: ${numerologicalData.universalDayNumber}
- Personal Month Number: ${numerologicalData.personalMonthNumber}
- Personal Year Number: ${numerologicalData.personalYearNumber}
- Current Phase: ${numerologicalData.currentPhase.phaseName}
- Phase Description: ${numerologicalData.currentPhase.description}

TEMPLATE: $template

Generate a prediction following the same format as the astrological prediction, but focused on numerological insights and vibrations. Emphasize how the numbers align and what opportunities or challenges they present.
''';
  }

  String _buildChinesePrompt(
    PredictionContext context,
    ChineseContext chineseData,
    String template,
  ) {
    final userProfile = context.userProfile;
    final chineseProfile = userProfile.chineseProfile;

    return '''
Create a personalized Chinese astrology prediction for ${context.date.toString().split(' ')[0]}.

USER PROFILE:
- Birth Year Animal: ${chineseProfile?.zodiacAnimal?.displayName ?? 'Unknown'}
- Birth Year Element: ${chineseProfile?.element?.toString().split('.').last ?? 'Unknown'}
- Birth Year Polarity: ${chineseProfile?.polarity?.toString().split('.').last ?? 'Unknown'}

CURRENT CHINESE ENERGIES:
- Day Animal: ${chineseData.dayAnimal.displayName}
- Day Element: ${chineseData.dayElement.toString().split('.').last}
- Day Polarity: ${chineseData.dayPolarity.toString().split('.').last}
- Season: ${chineseData.seasonInfo.season.toString().split('.').last}
- Favorable Directions: ${chineseData.favorableDirections.join(', ')}
- Favorable Colors: ${chineseData.favorableColors.join(', ')}

TEMPLATE: $template

Generate a prediction following the same format, but focused on Chinese astrology principles, elemental interactions, and how the day's energies interact with the user's birth year characteristics.
''';
  }

  String _buildAyurvedicPrompt(
    PredictionContext context,
    AyurvedicContext ayurvedicData,
    String template,
  ) {
    final userProfile = context.userProfile;
    final ayurvedicProfile = userProfile.ayurvedicProfile;

    return '''
Create a personalized Ayurvedic wellness prediction for ${context.date.toString().split(' ')[0]}.

USER PROFILE:
- Dominant Dosha: ${ayurvedicProfile?.dominantDosha?.displayName ?? 'Unknown'}
- Dosha Percentages: ${ayurvedicProfile?.doshaPercentages?.entries.map((e) => '${e.key.displayName}: ${e.value.toStringAsFixed(0)}%').join(', ') ?? 'Unknown'}

CURRENT AYURVEDIC CONDITIONS:
- Dominant Dosha of Day: ${ayurvedicData.dominantDoshaOfDay.displayName}
- Current Season: ${ayurvedicData.currentSeason.seasonName}
- Recommended Foods: ${ayurvedicData.recommendedFoods.join(', ')}
- Foods to Avoid: ${ayurvedicData.avoidedFoods.join(', ')}
- Recommended Activities: ${ayurvedicData.recommendedActivities.join(', ')}

TEMPLATE: $template

Generate a prediction following the same format, but focused on Ayurvedic wellness principles, dosha balancing, and practical health and lifestyle recommendations for the day.
''';
  }

  String _buildSummaryPrompt(
    List<DailyPrediction> predictions,
    PredictionContext context,
  ) {
    final predictionSummaries = predictions.map((p) => 
      '${p.type.displayName}: ${p.summary} (Mood: ${p.mood.displayName}, Energy: ${p.energyLevel})'
    ).join('\n');

    return '''
Create a unified daily summary from these individual predictions:

$predictionSummaries

USER PROFILE:
- Date: ${context.date.toString().split(' ')[0]}

Synthesize these predictions into a cohesive daily overview that includes:
1. Overall mood (excellent/good/neutral/challenging/difficult)
2. Overall energy level (1-10 scale)
3. Daily theme (main theme for the day, max 50 characters)
4. Top keywords (5-7 most important keywords from all predictions)
5. Main advice (synthesized guidance, max 150 words)
6. Primary focus (what to focus on today, max 80 characters)
7. Actions to avoid (3-5 specific things to avoid)
8. Favorable actions (3-5 specific things to do)

Make it coherent and highlight where different systems agree or complement each other.
''';
  }

  String _buildEnhancementPrompt(
    String baseContent,
    PredictionType type,
    Map<String, dynamic> enhancementData,
  ) {
    return '''
Enhance this ${type.displayName} prediction content:

ORIGINAL CONTENT:
$baseContent

ENHANCEMENT DATA:
${enhancementData.entries.map((e) => '${e.key}: ${e.value}').join('\n')}

Please enhance the content by:
1. Adding more specific details
2. Making it more personalized
3. Including practical examples
4. Improving the flow and readability
5. Ensuring mystical accuracy

Keep the same length and tone, but make it more engaging and insightful.
''';
  }

  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}

// These classes are now defined in core/services/ai_client.dart

abstract class PredictionTemplateService {
  String getAstrologicalTemplate(AstrologicalContext context);
  String getNumerologicalTemplate(NumerologicalContext context);
  String getChineseTemplate(ChineseContext context);
  String getAyurvedicTemplate(AyurvedicContext context);
}
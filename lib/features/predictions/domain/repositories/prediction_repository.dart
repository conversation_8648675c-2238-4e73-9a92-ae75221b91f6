import '../entities/prediction.dart';
import '../entities/prediction_context.dart';

abstract class PredictionRepository {
  // Daily predictions
  Future<PredictionCollection> generateDailyPredictions({
    required String userId,
    required DateTime date,
  });

  Future<PredictionCollection?> getDailyPredictions({
    required String userId,
    required DateTime date,
  });

  Future<PredictionCollection> saveDailyPredictions(PredictionCollection collection);

  Future<DailyPrediction?> getPredictionByType({
    required String userId,
    required DateTime date,
    required PredictionType type,
  });

  // Prediction history
  Future<List<PredictionCollection>> getPredictionHistory({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<List<DailyPrediction>> getUserPredictionsByType({
    required String userId,
    required PredictionType type,
    required int limit,
  });

  // Prediction management
  Future<void> markPredictionAsViewed({
    required String predictionId,
    required String userId,
  });

  Future<void> sharePrediction({
    required String predictionId,
    required String shareMethod,
  });

  Future<void> deletePrediction(String predictionId);

  Future<void> deleteUserPredictions(String userId);

  // Analytics and insights
  Future<Map<String, dynamic>> getPredictionAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<List<String>> getMostAccuratePredictionTypes(String userId);

  Future<Map<PredictionMood, int>> getMoodDistribution({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  // Caching and performance
  Future<void> preloadPredictions({
    required String userId,
    required List<DateTime> dates,
  });

  Future<bool> arePredictionsAvailable({
    required String userId,
    required DateTime date,
  });

  Future<void> clearPredictionCache(String userId);

  // Backup and restore
  Future<Map<String, dynamic>> exportPredictions(String userId);

  Future<void> importPredictions({
    required String userId,
    required Map<String, dynamic> data,
  });
}
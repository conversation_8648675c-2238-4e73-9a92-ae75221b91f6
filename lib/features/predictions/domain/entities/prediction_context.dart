import 'package:equatable/equatable.dart';
import '../../../user_profile/domain/entities/mystical_profile.dart';

class PredictionContext extends Equatable {
  final String userId;
  final DateTime date;
  final MysticalProfile userProfile;
  final AstrologicalContext astrologicalContext;
  final NumerologicalContext numerologicalContext;
  final ChineseContext chineseContext;
  final AyurvedicContext ayurvedicContext;
  final DateTime createdAt;

  const PredictionContext({
    required this.userId,
    required this.date,
    required this.userProfile,
    required this.astrologicalContext,
    required this.numerologicalContext,
    required this.chineseContext,
    required this.ayurvedicContext,
    required this.createdAt,
  });

  @override
  List<Object> get props => [
        userId,
        date,
        userProfile,
        astrologicalContext,
        numerologicalContext,
        chineseContext,
        ayurvedicContext,
        createdAt,
      ];
}

class AstrologicalContext extends Equatable {
  final DateTime date;
  final List<PlanetaryTransit> transits;
  final MoonPhase moonPhase;
  final List<AspectEvent> majorAspects;
  final RetrogradePlanets retrogradeInfo;
  final SeasonalInfo seasonalInfo;

  const AstrologicalContext({
    required this.date,
    required this.transits,
    required this.moonPhase,
    required this.majorAspects,
    required this.retrogradeInfo,
    required this.seasonalInfo,
  });

  @override
  List<Object> get props => [date, transits, moonPhase, majorAspects, retrogradeInfo, seasonalInfo];
}

class NumerologicalContext extends Equatable {
  final DateTime date;
  final int personalDayNumber;
  final int universalDayNumber;
  final int personalMonthNumber;
  final int personalYearNumber;
  final List<int> activeCycles;
  final NumerologicalPhase currentPhase;

  const NumerologicalContext({
    required this.date,
    required this.personalDayNumber,
    required this.universalDayNumber,
    required this.personalMonthNumber,
    required this.personalYearNumber,
    required this.activeCycles,
    required this.currentPhase,
  });

  @override
  List<Object> get props => [
        date,
        personalDayNumber,
        universalDayNumber,
        personalMonthNumber,
        personalYearNumber,
        activeCycles,
        currentPhase,
      ];
}

class ChineseContext extends Equatable {
  final DateTime date;
  final ChineseZodiacAnimal dayAnimal;
  final ChineseElement dayElement;
  final YinYang dayPolarity;
  final int dayInCycle;
  final ChineseSeasonInfo seasonInfo;
  final List<String> favorableDirections;
  final List<String> favorableColors;

  const ChineseContext({
    required this.date,
    required this.dayAnimal,
    required this.dayElement,
    required this.dayPolarity,
    required this.dayInCycle,
    required this.seasonInfo,
    required this.favorableDirections,
    required this.favorableColors,
  });

  @override
  List<Object> get props => [
        date,
        dayAnimal,
        dayElement,
        dayPolarity,
        dayInCycle,
        seasonInfo,
        favorableDirections,
        favorableColors,
      ];
}

class AyurvedicContext extends Equatable {
  final DateTime date;
  final Dosha dominantDoshaOfDay;
  final AyurvedicSeason currentSeason;
  final List<String> recommendedFoods;
  final List<String> avoidedFoods;
  final List<String> recommendedActivities;
  final TimeOfDayRecommendations timeRecommendations;

  const AyurvedicContext({
    required this.date,
    required this.dominantDoshaOfDay,
    required this.currentSeason,
    required this.recommendedFoods,
    required this.avoidedFoods,
    required this.recommendedActivities,
    required this.timeRecommendations,
  });

  @override
  List<Object> get props => [
        date,
        dominantDoshaOfDay,
        currentSeason,
        recommendedFoods,
        avoidedFoods,
        recommendedActivities,
        timeRecommendations,
      ];
}

// Supporting classes for astrological context
class PlanetaryTransit extends Equatable {
  final Planet planet;
  final ZodiacSign fromSign;
  final ZodiacSign toSign;
  final DateTime exactTime;
  final TransitType type;
  final double influence; // 0.0 to 1.0

  const PlanetaryTransit({
    required this.planet,
    required this.fromSign,
    required this.toSign,
    required this.exactTime,
    required this.type,
    required this.influence,
  });

  @override
  List<Object> get props => [planet, fromSign, toSign, exactTime, type, influence];
}

class MoonPhase extends Equatable {
  final MoonPhaseType phase;
  final double illumination; // 0.0 to 1.0
  final ZodiacSign moonSign;
  final DateTime exactTime;

  const MoonPhase({
    required this.phase,
    required this.illumination,
    required this.moonSign,
    required this.exactTime,
  });

  @override
  List<Object> get props => [phase, illumination, moonSign, exactTime];
}

class AspectEvent extends Equatable {
  final Planet planet1;
  final Planet planet2;
  final AspectType aspectType;
  final DateTime exactTime;
  final double orb;
  final bool isApplying;

  const AspectEvent({
    required this.planet1,
    required this.planet2,
    required this.aspectType,
    required this.exactTime,
    required this.orb,
    required this.isApplying,
  });

  @override
  List<Object> get props => [planet1, planet2, aspectType, exactTime, orb, isApplying];
}

class RetrogradePlanets extends Equatable {
  final List<Planet> retrogradeList;
  final List<Planet> stationaryList;
  final Map<Planet, DateTime> nextDirectDates;

  const RetrogradePlanets({
    required this.retrogradeList,
    required this.stationaryList,
    required this.nextDirectDates,
  });

  @override
  List<Object> get props => [retrogradeList, stationaryList, nextDirectDates];
}

class SeasonalInfo extends Equatable {
  final AstrologicalSeason season;
  final DateTime seasonStart;
  final DateTime seasonEnd;
  final List<String> seasonalThemes;

  const SeasonalInfo({
    required this.season,
    required this.seasonStart,
    required this.seasonEnd,
    required this.seasonalThemes,
  });

  @override
  List<Object> get props => [season, seasonStart, seasonEnd, seasonalThemes];
}

// Supporting classes for numerological context
class NumerologicalPhase extends Equatable {
  final String phaseName;
  final String description;
  final List<String> opportunities;
  final List<String> challenges;

  const NumerologicalPhase({
    required this.phaseName,
    required this.description,
    required this.opportunities,
    required this.challenges,
  });

  @override
  List<Object> get props => [phaseName, description, opportunities, challenges];
}

// Supporting classes for Chinese context
class ChineseSeasonInfo extends Equatable {
  final ChineseSeason season;
  final ChineseElement seasonElement;
  final List<String> seasonalAdvice;

  const ChineseSeasonInfo({
    required this.season,
    required this.seasonElement,
    required this.seasonalAdvice,
  });

  @override
  List<Object> get props => [season, seasonElement, seasonalAdvice];
}

// Supporting classes for Ayurvedic context
class AyurvedicSeason extends Equatable {
  final String seasonName;
  final Dosha dominantDosha;
  final List<String> characteristics;

  const AyurvedicSeason({
    required this.seasonName,
    required this.dominantDosha,
    required this.characteristics,
  });

  @override
  List<Object> get props => [seasonName, dominantDosha, characteristics];
}

class TimeOfDayRecommendations extends Equatable {
  final Map<String, List<String>> timeSlots; // "6-10" -> ["meditation", "light exercise"]

  const TimeOfDayRecommendations({
    required this.timeSlots,
  });

  @override
  List<Object> get props => [timeSlots];
}

// Enums
enum TransitType {
  entering,
  leaving,
  stationary,
  retrograde,
  direct,
}

enum MoonPhaseType {
  newMoon,
  waxingCrescent,
  firstQuarter,
  waxingGibbous,
  fullMoon,
  waningGibbous,
  lastQuarter,
  waningCrescent,
}

enum AstrologicalSeason {
  spring,
  summer,
  autumn,
  winter,
}

enum ChineseSeason {
  spring,
  summer,
  lateSummer,
  autumn,
  winter,
}

// Extension methods
extension MoonPhaseTypeExtension on MoonPhaseType {
  String get displayName {
    switch (this) {
      case MoonPhaseType.newMoon:
        return 'New Moon';
      case MoonPhaseType.waxingCrescent:
        return 'Waxing Crescent';
      case MoonPhaseType.firstQuarter:
        return 'First Quarter';
      case MoonPhaseType.waxingGibbous:
        return 'Waxing Gibbous';
      case MoonPhaseType.fullMoon:
        return 'Full Moon';
      case MoonPhaseType.waningGibbous:
        return 'Waning Gibbous';
      case MoonPhaseType.lastQuarter:
        return 'Last Quarter';
      case MoonPhaseType.waningCrescent:
        return 'Waning Crescent';
    }
  }

  String get emoji {
    switch (this) {
      case MoonPhaseType.newMoon:
        return '🌑';
      case MoonPhaseType.waxingCrescent:
        return '🌒';
      case MoonPhaseType.firstQuarter:
        return '🌓';
      case MoonPhaseType.waxingGibbous:
        return '🌔';
      case MoonPhaseType.fullMoon:
        return '🌕';
      case MoonPhaseType.waningGibbous:
        return '🌖';
      case MoonPhaseType.lastQuarter:
        return '🌗';
      case MoonPhaseType.waningCrescent:
        return '🌘';
    }
  }

  String get meaning {
    switch (this) {
      case MoonPhaseType.newMoon:
        return 'New beginnings, fresh starts, setting intentions';
      case MoonPhaseType.waxingCrescent:
        return 'Growth, taking action, moving forward';
      case MoonPhaseType.firstQuarter:
        return 'Decision making, overcoming challenges';
      case MoonPhaseType.waxingGibbous:
        return 'Refinement, adjustment, perseverance';
      case MoonPhaseType.fullMoon:
        return 'Culmination, harvest, celebration, release';
      case MoonPhaseType.waningGibbous:
        return 'Gratitude, sharing, giving back';
      case MoonPhaseType.lastQuarter:
        return 'Release, forgiveness, letting go';
      case MoonPhaseType.waningCrescent:
        return 'Rest, reflection, preparation';
    }
  }
}
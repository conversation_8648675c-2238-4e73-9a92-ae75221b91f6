import 'package:equatable/equatable.dart';

class DailyPrediction extends Equatable {
  final String id;
  final String userId;
  final DateTime date;
  final PredictionType type;
  final String title;
  final String content;
  final String summary;
  final List<String> keywords;
  final PredictionMood mood;
  final int energyLevel; // 1-10 scale
  final List<String> luckyNumbers;
  final List<String> luckyColors;
  final String advice;
  final String warning;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime? viewedAt;
  final bool isShared;

  const DailyPrediction({
    required this.id,
    required this.userId,
    required this.date,
    required this.type,
    required this.title,
    required this.content,
    required this.summary,
    required this.keywords,
    required this.mood,
    required this.energyLevel,
    required this.luckyNumbers,
    required this.luckyColors,
    required this.advice,
    required this.warning,
    required this.metadata,
    required this.createdAt,
    this.viewedAt,
    this.isShared = false,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        date,
        type,
        title,
        content,
        summary,
        keywords,
        mood,
        energyLevel,
        luckyNumbers,
        luckyColors,
        advice,
        warning,
        metadata,
        createdAt,
        viewedAt,
        isShared,
      ];

  DailyPrediction copyWith({
    String? id,
    String? userId,
    DateTime? date,
    PredictionType? type,
    String? title,
    String? content,
    String? summary,
    List<String>? keywords,
    PredictionMood? mood,
    int? energyLevel,
    List<String>? luckyNumbers,
    List<String>? luckyColors,
    String? advice,
    String? warning,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? viewedAt,
    bool? isShared,
  }) {
    return DailyPrediction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      type: type ?? this.type,
      title: title ?? this.title,
      content: content ?? this.content,
      summary: summary ?? this.summary,
      keywords: keywords ?? this.keywords,
      mood: mood ?? this.mood,
      energyLevel: energyLevel ?? this.energyLevel,
      luckyNumbers: luckyNumbers ?? this.luckyNumbers,
      luckyColors: luckyColors ?? this.luckyColors,
      advice: advice ?? this.advice,
      warning: warning ?? this.warning,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      viewedAt: viewedAt ?? this.viewedAt,
      isShared: isShared ?? this.isShared,
    );
  }

  bool get isViewed => viewedAt != null;
  bool get isToday => _isSameDay(date, DateTime.now());
  bool get isPast => date.isBefore(DateTime.now()) && !isToday;
  bool get isFuture => date.isAfter(DateTime.now()) && !isToday;

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}

class PredictionCollection extends Equatable {
  final String userId;
  final DateTime date;
  final List<DailyPrediction> predictions;
  final PredictionSummary summary;
  final DateTime createdAt;

  const PredictionCollection({
    required this.userId,
    required this.date,
    required this.predictions,
    required this.summary,
    required this.createdAt,
  });

  @override
  List<Object> get props => [userId, date, predictions, summary, createdAt];

  DailyPrediction? getPredictionByType(PredictionType type) {
    try {
      return predictions.firstWhere((p) => p.type == type);
    } catch (e) {
      return null;
    }
  }

  bool get isComplete => predictions.length >= 4; // All 4 types
  bool get hasAstrological => predictions.any((p) => p.type == PredictionType.astrological);
  bool get hasNumerological => predictions.any((p) => p.type == PredictionType.numerological);
  bool get hasChinese => predictions.any((p) => p.type == PredictionType.chinese);
  bool get hasAyurvedic => predictions.any((p) => p.type == PredictionType.ayurvedic);
}

class PredictionSummary extends Equatable {
  final PredictionMood overallMood;
  final int overallEnergyLevel;
  final String dailyTheme;
  final List<String> topKeywords;
  final String mainAdvice;
  final String primaryFocus;
  final List<String> avoidActions;
  final List<String> favorableActions;

  const PredictionSummary({
    required this.overallMood,
    required this.overallEnergyLevel,
    required this.dailyTheme,
    required this.topKeywords,
    required this.mainAdvice,
    required this.primaryFocus,
    required this.avoidActions,
    required this.favorableActions,
  });

  @override
  List<Object> get props => [
        overallMood,
        overallEnergyLevel,
        dailyTheme,
        topKeywords,
        mainAdvice,
        primaryFocus,
        avoidActions,
        favorableActions,
      ];
}

enum PredictionType {
  astrological,
  numerological,
  chinese,
  ayurvedic,
}

enum PredictionMood {
  excellent,
  good,
  neutral,
  challenging,
  difficult,
}

// Extension methods for better usability
extension PredictionTypeExtension on PredictionType {
  String get displayName {
    switch (this) {
      case PredictionType.astrological:
        return 'Astrological';
      case PredictionType.numerological:
        return 'Numerological';
      case PredictionType.chinese:
        return 'Chinese Astrology';
      case PredictionType.ayurvedic:
        return 'Ayurvedic';
    }
  }

  String get shortName {
    switch (this) {
      case PredictionType.astrological:
        return 'Astro';
      case PredictionType.numerological:
        return 'Numero';
      case PredictionType.chinese:
        return 'Chinese';
      case PredictionType.ayurvedic:
        return 'Ayurveda';
    }
  }

  String get icon {
    switch (this) {
      case PredictionType.astrological:
        return '⭐';
      case PredictionType.numerological:
        return '🔢';
      case PredictionType.chinese:
        return '🐲';
      case PredictionType.ayurvedic:
        return '🧘';
    }
  }

  String get description {
    switch (this) {
      case PredictionType.astrological:
        return 'Based on planetary movements and your birth chart';
      case PredictionType.numerological:
        return 'Based on your personal numbers and date vibrations';
      case PredictionType.chinese:
        return 'Based on Chinese zodiac and elemental energies';
      case PredictionType.ayurvedic:
        return 'Based on your dosha and daily wellness guidance';
    }
  }
}

extension PredictionMoodExtension on PredictionMood {
  String get displayName {
    switch (this) {
      case PredictionMood.excellent:
        return 'Excellent';
      case PredictionMood.good:
        return 'Good';
      case PredictionMood.neutral:
        return 'Neutral';
      case PredictionMood.challenging:
        return 'Challenging';
      case PredictionMood.difficult:
        return 'Difficult';
    }
  }

  String get emoji {
    switch (this) {
      case PredictionMood.excellent:
        return '🌟';
      case PredictionMood.good:
        return '😊';
      case PredictionMood.neutral:
        return '😐';
      case PredictionMood.challenging:
        return '⚠️';
      case PredictionMood.difficult:
        return '🚨';
    }
  }

  String get color {
    switch (this) {
      case PredictionMood.excellent:
        return '#10B981'; // Green
      case PredictionMood.good:
        return '#3B82F6'; // Blue
      case PredictionMood.neutral:
        return '#6B7280'; // Gray
      case PredictionMood.challenging:
        return '#F59E0B'; // Amber
      case PredictionMood.difficult:
        return '#EF4444'; // Red
    }
  }

  int get energyRange {
    switch (this) {
      case PredictionMood.excellent:
        return 9; // 8-10
      case PredictionMood.good:
        return 7; // 6-8
      case PredictionMood.neutral:
        return 5; // 4-6
      case PredictionMood.challenging:
        return 3; // 2-4
      case PredictionMood.difficult:
        return 1; // 1-2
    }
  }
}
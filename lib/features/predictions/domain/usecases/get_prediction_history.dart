import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/prediction.dart';
import '../repositories/prediction_repository.dart';

class GetPredictionHistory implements UseCase<List<DailyPrediction>, GetPredictionHistoryParams> {
  final PredictionRepository repository;

  GetPredictionHistory(this.repository);

  @override
  Future<Either<Failure, List<DailyPrediction>>> call(GetPredictionHistoryParams params) async {
    return await repository.getPredictionHistory(params.userId, limit: params.limit);
  }
}

class GetPredictionHistoryParams {
  final String userId;
  final int limit;

  GetPredictionHistoryParams({
    required this.userId,
    this.limit = 30,
  });
}
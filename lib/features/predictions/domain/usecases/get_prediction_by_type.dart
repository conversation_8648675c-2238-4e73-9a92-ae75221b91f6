import '../entities/prediction.dart';
import '../repositories/prediction_repository.dart';

class GetPredictionByType {
  final PredictionRepository repository;

  GetPredictionByType(this.repository);

  Future<DailyPrediction?> call({
    required String userId,
    required DateTime date,
    required PredictionType type,
  }) async {
    return await repository.getPredictionByType(
      userId: userId,
      date: date,
      type: type,
    );
  }
}
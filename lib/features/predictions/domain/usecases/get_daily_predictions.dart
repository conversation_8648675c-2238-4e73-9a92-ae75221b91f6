import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/prediction.dart';
import '../repositories/prediction_repository.dart';

class GetDailyPredictions implements UseCase<PredictionCollection?, GetDailyPredictionsParams> {
  final PredictionRepository repository;

  GetDailyPredictions(this.repository);

  @override
  Future<Either<Failure, PredictionCollection?>> call(GetDailyPredictionsParams params) async {
    return await repository.getPredictionCollection(params.userId, params.date);
  }
}

class GetDailyPredictionsParams {
  final String userId;
  final DateTime date;

  GetDailyPredictionsParams({
    required this.userId,
    required this.date,
  });
}
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/prediction_repository.dart';

class SharePrediction implements UseCase<void, SharePredictionParams> {
  final PredictionRepository repository;

  SharePrediction(this.repository);

  @override
  Future<Either<Failure, void>> call(SharePredictionParams params) async {
    return await repository.sharePrediction(params.predictionId, params.shareMethod);
  }
}

class SharePredictionParams {
  final String predictionId;
  final String shareMethod;

  SharePredictionParams({
    required this.predictionId,
    required this.shareMethod,
  });
}
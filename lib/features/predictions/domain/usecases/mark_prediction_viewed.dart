import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/prediction_repository.dart';

class MarkPredictionViewed implements UseCase<void, MarkPredictionViewedParams> {
  final PredictionRepository repository;

  MarkPredictionViewed(this.repository);

  @override
  Future<Either<Failure, void>> call(MarkPredictionViewedParams params) async {
    return await repository.markPredictionAsViewed(params.predictionId);
  }
}

class MarkPredictionViewedParams {
  final String predictionId;

  MarkPredictionViewedParams({
    required this.predictionId,
  });
}
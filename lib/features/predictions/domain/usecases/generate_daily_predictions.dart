import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../user_profile/domain/entities/mystical_profile.dart';
import '../../../user_profile/domain/repositories/user_profile_repository.dart';
import '../entities/prediction.dart';
import '../entities/prediction_context.dart';
import '../repositories/prediction_repository.dart';
import '../services/ai_prediction_service.dart';
import '../services/prediction_context_service.dart';

class GenerateDailyPredictions implements UseCase<PredictionCollection, GeneratePredictionsParams> {
  final PredictionRepository repository;
  final PredictionContextService contextService;
  final AIPredictionService aiService;
  final UserProfileRepository userProfileRepository;

  GenerateDailyPredictions({
    required this.repository,
    required this.contextService,
    required this.aiService,
    required this.userProfileRepository,
  });

  @override
  Future<Either<Failure, PredictionCollection>> call(GeneratePredictionsParams params) async {
    try {
      // 1. Get user's mystical profile
      final profileResult = await userProfileRepository.getMysticalProfile(params.userId);
      if (profileResult.isLeft()) {
        return profileResult.fold(
          (failure) => Left(failure),
          (profile) => throw Exception('Unexpected right result'),
        );
      }

      final MysticalProfile? userProfile = profileResult.fold(
        (failure) => null,
        (profile) => profile,
      );

      if (userProfile == null) {
        return const Left(ServerFailure(message: 'User profile not found'));
      }

      // 2. Calculate prediction context
      final context = await contextService.calculatePredictionContext(
        userId: params.userId,
        date: params.date,
        userProfile: userProfile,
      );

      // 3. Generate individual predictions
      final predictions = <DailyPrediction>[];

      // Generate astrological prediction
      if (params.types.contains(PredictionType.astrological)) {
        final astrologicalPrediction = await aiService.generateAstrologicalPrediction(
          context: context,
          astrologicalData: context.astrologicalContext,
        );
        predictions.add(astrologicalPrediction);
      }

      // Generate numerological prediction
      if (params.types.contains(PredictionType.numerological)) {
        final numerologicalPrediction = await aiService.generateNumerologicalPrediction(
          context: context,
          numerologicalData: context.numerologicalContext,
        );
        predictions.add(numerologicalPrediction);
      }

      // Generate Chinese prediction
      if (params.types.contains(PredictionType.chinese)) {
        final chinesePrediction = await aiService.generateChinesePrediction(
          context: context,
          chineseData: context.chineseContext,
        );
        predictions.add(chinesePrediction);
      }

      // Generate Ayurvedic prediction
      if (params.types.contains(PredictionType.ayurvedic)) {
        final ayurvedicPrediction = await aiService.generateAyurvedicPrediction(
          context: context,
          ayurvedicData: context.ayurvedicContext,
        );
        predictions.add(ayurvedicPrediction);
      }

      // 4. Generate daily summary
      final summary = await aiService.generateDailySummary(
        predictions: predictions,
        context: context,
      );

      // 5. Create prediction collection
      final collection = PredictionCollection(
        id: _generateCollectionId(),
        userId: params.userId,
        date: params.date,
        predictions: predictions,
        summary: summary,
        createdAt: DateTime.now(),
      );

      // 6. Save collection to repository
      final saveCollectionResult = await repository.createPredictionCollection(collection);
      if (saveCollectionResult.isLeft()) {
        return saveCollectionResult;
      }

      // 7. Save individual predictions
      for (final prediction in predictions) {
        final savePredictionResult = await repository.createPrediction(prediction);
        if (savePredictionResult.isLeft()) {
          // Continue with other predictions even if one fails
          print('Failed to save prediction: ${prediction.type}');
        }
      }

      return Right(collection);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to generate predictions: ${e.toString()}'));
    }
  }

  String _generateCollectionId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}

class GeneratePredictionsParams {
  final String userId;
  final DateTime date;
  final List<PredictionType> types;

  GeneratePredictionsParams({
    required this.userId,
    required this.date,
    required this.types,
  });

  factory GeneratePredictionsParams.all({
    required String userId,
    required DateTime date,
  }) {
    return GeneratePredictionsParams(
      userId: userId,
      date: date,
      types: PredictionType.values,
    );
  }

  factory GeneratePredictionsParams.single({
    required String userId,
    required DateTime date,
    required PredictionType type,
  }) {
    return GeneratePredictionsParams(
      userId: userId,
      date: date,
      types: [type],
    );
  }
}
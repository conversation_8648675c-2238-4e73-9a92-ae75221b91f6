import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/prediction_model.dart';

abstract class PredictionRemoteDataSource {
  Future<PredictionCollectionModel?> getDailyPredictions({
    required String userId,
    required DateTime date,
  });

  Future<PredictionCollectionModel> saveDailyPredictions(PredictionCollectionModel collection);

  Future<DailyPredictionModel?> getPredictionByType({
    required String userId,
    required DateTime date,
    required String type,
  });

  Future<List<PredictionCollectionModel>> getPredictionHistory({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<List<DailyPredictionModel>> getUserPredictionsByType({
    required String userId,
    required String type,
    required int limit,
  });

  Future<void> markPredictionAsViewed({
    required String predictionId,
    required String userId,
  });

  Future<void> sharePrediction({
    required String predictionId,
    required String shareMethod,
  });

  Future<void> deletePrediction(String predictionId);

  Future<void> deleteUserPredictions(String userId);

  Future<Map<String, dynamic>> getPredictionAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<bool> arePredictionsAvailable({
    required String userId,
    required DateTime date,
  });

  Future<Map<String, dynamic>> exportPredictions(String userId);

  Future<void> importPredictions({
    required String userId,
    required Map<String, dynamic> data,
  });
}

class PredictionRemoteDataSourceImpl implements PredictionRemoteDataSource {
  final SupabaseClient supabaseClient;

  PredictionRemoteDataSourceImpl({required this.supabaseClient});

  static const String _predictionsTable = 'daily_predictions';
  static const String _predictionCollectionsTable = 'prediction_collections';
  static const String _predictionSharesTable = 'prediction_shares';
  static const String _predictionAnalyticsTable = 'prediction_analytics';

  @override
  Future<PredictionCollectionModel?> getDailyPredictions({
    required String userId,
    required DateTime date,
  }) async {
    try {
      final dateString = _formatDate(date);
      
      final response = await supabaseClient
          .from(_predictionCollectionsTable)
          .select('''
            *,
            predictions:$_predictionsTable(*)
          ''')
          .eq('user_id', userId)
          .eq('date', dateString)
          .maybeSingle();

      if (response == null) return null;

      return PredictionCollectionModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get daily predictions: ${e.toString()}',
      );
    }
  }

  @override
  Future<PredictionCollectionModel> saveDailyPredictions(PredictionCollectionModel collection) async {
    try {
      // Save collection metadata
      final collectionData = collection.toJson();
      final predictions = collectionData.remove('predictions') as List<dynamic>;

      final collectionResponse = await supabaseClient
          .from(_predictionCollectionsTable)
          .upsert(collectionData)
          .select()
          .single();

      // Save individual predictions
      final predictionInserts = predictions.map((p) {
        final predictionData = Map<String, dynamic>.from(p);
        predictionData['collection_id'] = collectionResponse['id'];
        return predictionData;
      }).toList();

      if (predictionInserts.isNotEmpty) {
        await supabaseClient
            .from(_predictionsTable)
            .upsert(predictionInserts);
      }

      // Return the saved collection with predictions
      return await getDailyPredictions(
        userId: collection.userId,
        date: collection.date,
      ) ?? collection;
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to save daily predictions: ${e.toString()}',
      );
    }
  }

  @override
  Future<DailyPredictionModel?> getPredictionByType({
    required String userId,
    required DateTime date,
    required String type,
  }) async {
    try {
      final dateString = _formatDate(date);
      
      final response = await supabaseClient
          .from(_predictionsTable)
          .select()
          .eq('user_id', userId)
          .eq('date', dateString)
          .eq('type', type)
          .maybeSingle();

      if (response == null) return null;

      return DailyPredictionModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get prediction by type: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<PredictionCollectionModel>> getPredictionHistory({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final startDateString = _formatDate(startDate);
      final endDateString = _formatDate(endDate);
      
      final response = await supabaseClient
          .from(_predictionCollectionsTable)
          .select('''
            *,
            predictions:$_predictionsTable(*)
          ''')
          .eq('user_id', userId)
          .gte('date', startDateString)
          .lte('date', endDateString)
          .order('date', ascending: false);

      return response
          .map((json) => PredictionCollectionModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get prediction history: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<DailyPredictionModel>> getUserPredictionsByType({
    required String userId,
    required String type,
    required int limit,
  }) async {
    try {
      final response = await supabaseClient
          .from(_predictionsTable)
          .select()
          .eq('user_id', userId)
          .eq('type', type)
          .order('date', ascending: false)
          .limit(limit);

      return response
          .map((json) => DailyPredictionModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get user predictions by type: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> markPredictionAsViewed({
    required String predictionId,
    required String userId,
  }) async {
    try {
      await supabaseClient
          .from(_predictionsTable)
          .update({
            'viewed_at': DateTime.now().toIso8601String(),
          })
          .eq('id', predictionId)
          .eq('user_id', userId);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to mark prediction as viewed: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> sharePrediction({
    required String predictionId,
    required String shareMethod,
  }) async {
    try {
      // Mark prediction as shared
      await supabaseClient
          .from(_predictionsTable)
          .update({'is_shared': true})
          .eq('id', predictionId);

      // Log the share action
      await supabaseClient
          .from(_predictionSharesTable)
          .insert({
            'prediction_id': predictionId,
            'share_method': shareMethod,
            'shared_at': DateTime.now().toIso8601String(),
          });
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to share prediction: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> deletePrediction(String predictionId) async {
    try {
      await supabaseClient
          .from(_predictionsTable)
          .delete()
          .eq('id', predictionId);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to delete prediction: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> deleteUserPredictions(String userId) async {
    try {
      // Delete predictions
      await supabaseClient
          .from(_predictionsTable)
          .delete()
          .eq('user_id', userId);

      // Delete collections
      await supabaseClient
          .from(_predictionCollectionsTable)
          .delete()
          .eq('user_id', userId);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to delete user predictions: ${e.toString()}',
      );
    }
  }

  @override
  Future<Map<String, dynamic>> getPredictionAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final startDateString = _formatDate(startDate);
      final endDateString = _formatDate(endDate);

      // Get basic analytics
      final predictions = await supabaseClient
          .from(_predictionsTable)
          .select('type, mood, energy_level, viewed_at, is_shared')
          .eq('user_id', userId)
          .gte('date', startDateString)
          .lte('date', endDateString);

      // Calculate analytics
      final analytics = _calculateAnalytics(predictions);
      
      return analytics;
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get prediction analytics: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> arePredictionsAvailable({
    required String userId,
    required DateTime date,
  }) async {
    try {
      final dateString = _formatDate(date);
      
      final response = await supabaseClient
          .from(_predictionCollectionsTable)
          .select('id')
          .eq('user_id', userId)
          .eq('date', dateString)
          .maybeSingle();

      return response != null;
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to check prediction availability: ${e.toString()}',
      );
    }
  }

  @override
  Future<Map<String, dynamic>> exportPredictions(String userId) async {
    try {
      final collections = await supabaseClient
          .from(_predictionCollectionsTable)
          .select('''
            *,
            predictions:$_predictionsTable(*)
          ''')
          .eq('user_id', userId)
          .order('date', ascending: false);

      return {
        'user_id': userId,
        'exported_at': DateTime.now().toIso8601String(),
        'version': '1.0',
        'collections': collections,
      };
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to export predictions: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> importPredictions({
    required String userId,
    required Map<String, dynamic> data,
  }) async {
    try {
      if (!data.containsKey('collections')) {
        throw ServerException(message: 'Invalid import data: missing collections');
      }

      final collections = data['collections'] as List<dynamic>;
      
      for (final collectionData in collections) {
        final collection = PredictionCollectionModel.fromJson(collectionData);
        await saveDailyPredictions(collection);
      }
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(
        message: 'Failed to import predictions: ${e.toString()}',
      );
    }
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Map<String, dynamic> _calculateAnalytics(List<Map<String, dynamic>> predictions) {
    if (predictions.isEmpty) {
      return {
        'total_predictions': 0,
        'viewed_predictions': 0,
        'shared_predictions': 0,
        'view_rate': 0.0,
        'share_rate': 0.0,
        'mood_distribution': {},
        'average_energy_level': 0.0,
        'most_common_type': null,
      };
    }

    final total = predictions.length;
    final viewed = predictions.where((p) => p['viewed_at'] != null).length;
    final shared = predictions.where((p) => p['is_shared'] == true).length;

    final moodCounts = <String, int>{};
    double totalEnergy = 0.0;
    final typeCounts = <String, int>{};

    for (final prediction in predictions) {
      final mood = prediction['mood'] as String;
      moodCounts[mood] = (moodCounts[mood] ?? 0) + 1;

      final energy = (prediction['energy_level'] as num).toDouble();
      totalEnergy += energy;

      final type = prediction['type'] as String;
      typeCounts[type] = (typeCounts[type] ?? 0) + 1;
    }

    final mostCommonType = typeCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return {
      'total_predictions': total,
      'viewed_predictions': viewed,
      'shared_predictions': shared,
      'view_rate': viewed / total,
      'share_rate': shared / total,
      'mood_distribution': moodCounts,
      'average_energy_level': totalEnergy / total,
      'most_common_type': mostCommonType,
      'type_distribution': typeCounts,
    };
  }
}
import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/prediction.dart';
import '../../domain/repositories/prediction_repository.dart';
import '../datasources/prediction_remote_datasource.dart';
import '../models/prediction_model.dart';

class PredictionRepositoryImpl implements PredictionRepository {
  final PredictionRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  PredictionRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<DailyPrediction>>> getDailyPredictions(
    String userId,
    DateTime date,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final remotePredictions = await remoteDataSource.getDailyPredictions(userId, date);
        return Right(remotePredictions.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, PredictionCollection?>> getPredictionCollection(
    String userId,
    DateTime date,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final collectionModel = await remoteDataSource.getPredictionCollection(userId, date);
        if (collectionModel == null) {
          return const Right(null);
        }

        // Get all predictions for this collection
        final predictionsModels = await remoteDataSource.getDailyPredictions(userId, date);
        final predictions = predictionsModels.map((model) => model.toEntity()).toList();
        
        return Right(collectionModel.toEntity(predictions));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, DailyPrediction>> createPrediction(DailyPrediction prediction) async {
    if (await networkInfo.isConnected) {
      try {
        final predictionModel = DailyPredictionModel.fromEntity(prediction);
        final createdModel = await remoteDataSource.createPrediction(predictionModel);
        return Right(createdModel.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, PredictionCollection>> createPredictionCollection(
    PredictionCollection collection,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final collectionModel = PredictionCollectionModel.fromEntity(collection);
        final createdModel = await remoteDataSource.createPredictionCollection(collectionModel);
        return Right(createdModel.toEntity(collection.predictions));
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, DailyPrediction>> updatePrediction(DailyPrediction prediction) async {
    if (await networkInfo.isConnected) {
      try {
        final predictionModel = DailyPredictionModel.fromEntity(prediction);
        final updatedModel = await remoteDataSource.updatePrediction(predictionModel);
        return Right(updatedModel.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> markPredictionAsViewed(String predictionId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.markPredictionAsViewed(predictionId);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> sharePrediction(String predictionId, String shareMethod) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.sharePrediction(predictionId, shareMethod);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<DailyPrediction>>> getPredictionHistory(
    String userId, {
    int limit = 30,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final historyModels = await remoteDataSource.getPredictionHistory(userId, limit: limit);
        return Right(historyModels.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deletePrediction(String predictionId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deletePrediction(predictionId);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(ServerFailure(message: 'No internet connection'));
    }
  }
}
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/prediction.dart';
import '../../../user_profile/domain/entities/mystical_profile.dart';

part 'prediction_model.g.dart';

@JsonSerializable()
class DailyPredictionModel {
  final String id;
  @Json<PERSON>ey(name: 'collection_id')
  final String? collectionId;
  @<PERSON>son<PERSON><PERSON>(name: 'user_id')
  final String userId;
  final DateTime date;
  final String type;
  final String title;
  final String content;
  final String summary;
  final List<String> keywords;
  final String mood;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'energy_level')
  final int energyLevel;
  @<PERSON>son<PERSON><PERSON>(name: 'lucky_numbers')
  final List<String> luckyNumbers;
  @J<PERSON><PERSON><PERSON>(name: 'lucky_colors')
  final List<String> luckyColors;
  final String advice;
  final String warning;
  final Map<String, dynamic> metadata;
  @<PERSON>son<PERSON>ey(name: 'viewed_at')
  final DateTime? viewedAt;
  @Json<PERSON>ey(name: 'is_shared')
  final bool isShared;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;

  DailyPredictionModel({
    required this.id,
    this.collectionId,
    required this.userId,
    required this.date,
    required this.type,
    required this.title,
    required this.content,
    required this.summary,
    required this.keywords,
    required this.mood,
    required this.energyLevel,
    required this.luckyNumbers,
    required this.luckyColors,
    required this.advice,
    required this.warning,
    required this.metadata,
    this.viewedAt,
    required this.isShared,
    required this.createdAt,
  });

  factory DailyPredictionModel.fromJson(Map<String, dynamic> json) => 
      _$DailyPredictionModelFromJson(json);

  Map<String, dynamic> toJson() => _$DailyPredictionModelToJson(this);

  DailyPrediction toEntity() {
    return DailyPrediction(
      id: id,
      userId: userId,
      date: date,
      type: PredictionType.values.firstWhere(
        (e) => e.toString().split('.').last == type,
      ),
      title: title,
      content: content,
      summary: summary,
      keywords: keywords,
      mood: PredictionMood.values.firstWhere(
        (e) => e.toString().split('.').last == mood,
      ),
      energyLevel: energyLevel,
      luckyNumbers: luckyNumbers,
      luckyColors: luckyColors,
      advice: advice,
      warning: warning,
      metadata: metadata,
      viewedAt: viewedAt,
      isShared: isShared,
      createdAt: createdAt,
    );
  }

  factory DailyPredictionModel.fromEntity(DailyPrediction entity) {
    return DailyPredictionModel(
      id: entity.id,
      userId: entity.userId,
      date: entity.date,
      type: entity.type.toString().split('.').last,
      title: entity.title,
      content: entity.content,
      summary: entity.summary,
      keywords: entity.keywords,
      mood: entity.mood.toString().split('.').last,
      energyLevel: entity.energyLevel,
      luckyNumbers: entity.luckyNumbers,
      luckyColors: entity.luckyColors,
      advice: entity.advice,
      warning: entity.warning,
      metadata: entity.metadata,
      viewedAt: entity.viewedAt,
      isShared: entity.isShared,
      createdAt: entity.createdAt,
    );
  }
}

@JsonSerializable()
class PredictionCollectionModel {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final DateTime date;
  final Map<String, dynamic> summary;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  PredictionCollectionModel({
    required this.id,
    required this.userId,
    required this.date,
    required this.summary,
    required this.createdAt,
  });

  factory PredictionCollectionModel.fromJson(Map<String, dynamic> json) => 
      _$PredictionCollectionModelFromJson(json);

  Map<String, dynamic> toJson() => _$PredictionCollectionModelToJson(this);

  PredictionCollection toEntity(List<DailyPrediction> predictions) {
    // Parse summary from JSON
    final summaryData = PredictionSummary(
      overallMood: PredictionMood.values.firstWhere(
        (e) => e.toString().split('.').last == summary['overall_mood'],
        orElse: () => PredictionMood.neutral,
      ),
      overallEnergyLevel: summary['overall_energy_level'] ?? 5,
      dailyTheme: summary['daily_theme'] ?? '',
      topKeywords: List<String>.from(summary['top_keywords'] ?? []),
      mainAdvice: summary['main_advice'] ?? '',
      primaryFocus: summary['primary_focus'] ?? '',
      avoidActions: List<String>.from(summary['avoid_actions'] ?? []),
      favorableActions: List<String>.from(summary['favorable_actions'] ?? []),
    );

    return PredictionCollection(
      id: id,
      userId: userId,
      date: date,
      predictions: predictions,
      summary: summaryData,
      createdAt: createdAt,
    );
  }

  factory PredictionCollectionModel.fromEntity(PredictionCollection entity) {
    return PredictionCollectionModel(
      id: entity.id,
      userId: entity.userId,
      date: entity.date,
      summary: {
        'overall_mood': entity.summary.overallMood.toString().split('.').last,
        'overall_energy_level': entity.summary.overallEnergyLevel,
        'daily_theme': entity.summary.dailyTheme,
        'top_keywords': entity.summary.topKeywords,
        'main_advice': entity.summary.mainAdvice,
        'primary_focus': entity.summary.primaryFocus,
        'avoid_actions': entity.summary.avoidActions,
        'favorable_actions': entity.summary.favorableActions,
      },
      createdAt: entity.createdAt,
    );
  }
}

@JsonSerializable()
class PredictionShareModel {
  final String id;
  @JsonKey(name: 'prediction_id')
  final String predictionId;
  @JsonKey(name: 'share_method')
  final String shareMethod;
  @JsonKey(name: 'shared_at')
  final DateTime sharedAt;

  PredictionShareModel({
    required this.id,
    required this.predictionId,
    required this.shareMethod,
    required this.sharedAt,
  });

  factory PredictionShareModel.fromJson(Map<String, dynamic> json) => 
      _$PredictionShareModelFromJson(json);

  Map<String, dynamic> toJson() => _$PredictionShareModelToJson(this);
}
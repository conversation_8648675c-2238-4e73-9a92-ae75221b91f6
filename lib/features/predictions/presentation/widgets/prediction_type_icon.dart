import 'package:flutter/material.dart';
import '../../../../core/design_system/design_system.dart';
import '../../domain/entities/prediction.dart';

class PredictionTypeIcon extends StatelessWidget {
  final PredictionType type;
  final double size;
  final bool showBackground;

  const PredictionTypeIcon({
    super.key,
    required this.type,
    this.size = 48,
    this.showBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    final typeColor = _getTypeColor(type);
    
    return Container(
      width: size,
      height: size,
      padding: EdgeInsets.all(size * 0.25),
      decoration: showBackground ? BoxDecoration(
        color: typeColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(size * 0.25),
      ) : null,
      child: Center(
        child: Text(
          type.icon,
          style: TextStyle(fontSize: size * 0.5),
        ),
      ),
    );
  }

  Color _getTypeColor(PredictionType type) {
    switch (type) {
      case PredictionType.astrological:
        return AppColors.primary500;
      case PredictionType.numerological:
        return AppColors.info500;
      case PredictionType.chinese:
        return AppColors.warning500;
      case PredictionType.ayurvedic:
        return AppColors.success500;
    }
  }
}
import 'package:flutter/material.dart';
import '../../../../core/design_system/design_system.dart';

class PredictionLoadingShimmer extends StatefulWidget {
  const PredictionLoadingShimmer({super.key});

  @override
  State<PredictionLoadingShimmer> createState() => _PredictionLoadingShimmerState();
}

class _PredictionLoadingShimmerState extends State<PredictionLoadingShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Summary card shimmer
              _buildShimmerCard(height: 180),
              
              const SizedBox(height: 16),
              
              // Tab bar shimmer
              _buildShimmerTabBar(),
              
              const SizedBox(height: 16),
              
              // Content cards shimmer
              ...List.generate(3, (index) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: _buildShimmerCard(height: 120),
              )),
              
              // Loading message
              const SizedBox(height: 20),
              _buildLoadingMessage(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShimmerCard({required double height}) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.1 + (_animation.value * 0.1)),
            Colors.white.withValues(alpha: 0.05 + (_animation.value * 0.05)),
          ],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header shimmer
            Row(
              children: [
                _buildShimmerContainer(
                  width: 48,
                  height: 48,
                  borderRadius: BorderRadius.circular(12),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerContainer(
                        width: 120,
                        height: 14,
                        borderRadius: BorderRadius.circular(7),
                      ),
                      const SizedBox(height: 8),
                      _buildShimmerContainer(
                        width: double.infinity,
                        height: 18,
                        borderRadius: BorderRadius.circular(9),
                      ),
                    ],
                  ),
                ),
                _buildShimmerContainer(
                  width: 32,
                  height: 32,
                  borderRadius: BorderRadius.circular(8),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Content shimmer
            ...List.generate(height > 150 ? 4 : 2, (index) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: _buildShimmerContainer(
                width: index == 0 ? double.infinity : (index.isEven ? 200.0 : 150.0),
                height: 12,
                borderRadius: BorderRadius.circular(6),
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerTabBar() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1 + (_animation.value * 0.05)),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: List.generate(5, (index) => Expanded(
          child: Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: index == 0 
                  ? AppColors.primary500.withValues(alpha: 0.3 + (_animation.value * 0.2))
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: _buildShimmerContainer(
                width: 20,
                height: 20,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        )),
      ),
    );
  }

  Widget _buildShimmerContainer({
    required double width,
    required double height,
    required BorderRadius borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2 + (_animation.value * 0.1)),
        borderRadius: borderRadius,
      ),
    );
  }

  Widget _buildLoadingMessage() {
    return Column(
      children: [
        // Mystical loading animation
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                AppColors.primary500.withValues(alpha: 0.8),
                AppColors.primary500.withValues(alpha: 0.2),
              ],
            ),
          ),
          child: Center(
            child: RotationTransition(
              turns: _animationController,
              child: const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        Text(
          'Consulting the Stars...',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          _getLoadingMessage(),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getLoadingMessage() {
    final messages = [
      'Aligning planetary energies...',
      'Calculating numerological influences...',
      'Reading ancient wisdom...',
      'Interpreting cosmic patterns...',
      'Channeling mystical insights...',
      'Weaving your personal forecast...',
    ];
    
    final index = (_animationController.value * messages.length).floor() % messages.length;
    return messages[index];
  }
}
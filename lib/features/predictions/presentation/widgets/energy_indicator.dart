import 'package:flutter/material.dart';
import '../../../../core/design_system/design_system.dart';

class EnergyIndicator extends StatelessWidget {
  final int energyLevel;
  final double size;
  final bool showLabel;
  final Color? color;

  const EnergyIndicator({
    super.key,
    required this.energyLevel,
    this.size = 60,
    this.showLabel = true,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final indicatorColor = color ?? AppColors.primary500;
    
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              value: energyLevel / 10,
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
              strokeWidth: size * 0.1,
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                energyLevel.toString(),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: size * 0.25,
                ),
              ),
              if (showLabel)
                Text(
                  'Energy',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white70,
                    fontSize: size * 0.15,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
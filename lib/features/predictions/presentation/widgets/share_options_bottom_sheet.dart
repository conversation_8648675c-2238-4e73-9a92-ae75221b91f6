import 'package:flutter/material.dart';
import '../../../../core/design_system/design_system.dart';
import '../../domain/entities/prediction.dart';

class ShareOptionsBottomSheet extends StatelessWidget {
  final DailyPrediction prediction;
  final VoidCallback? onShareText;
  final VoidCallback? onShareEmail;
  final VoidCallback? onShareTwitter;
  final VoidCallback? onShareFacebook;
  
  const ShareOptionsBottomSheet({
    super.key,
    required this.prediction,
    this.onShareText,
    this.onShareEmail,
    this.onShareTwitter,
    this.onShareFacebook,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: AppColors.dark800,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Share Prediction',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _ShareOption(
                icon: Icons.message,
                label: 'Text',
                onTap: onShareText ?? () {},
              ),
              _ShareOption(
                icon: Icons.email,
                label: 'Email',
                onTap: onShareEmail ?? () {},
              ),
              _ShareOption(
                icon: Icons.share,
                label: 'Twitter',
                onTap: onShareTwitter ?? () {},
              ),
              _ShareOption(
                icon: Icons.facebook,
                label: 'Facebook',
                onTap: onShareFacebook ?? () {},
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  static void show({
    required BuildContext context,
    required DailyPrediction prediction,
    VoidCallback? onShareText,
    VoidCallback? onShareEmail,
    VoidCallback? onShareTwitter,
    VoidCallback? onShareFacebook,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => ShareOptionsBottomSheet(
        prediction: prediction,
        onShareText: onShareText,
        onShareEmail: onShareEmail,
        onShareTwitter: onShareTwitter,
        onShareFacebook: onShareFacebook,
      ),
    );
  }
}

class _ShareOption extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  
  const _ShareOption({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary500.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: AppColors.primary500),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}

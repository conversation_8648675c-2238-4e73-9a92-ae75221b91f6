import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/prediction.dart';
import '../../domain/usecases/generate_daily_predictions.dart';
import '../../domain/usecases/get_daily_predictions.dart';
import '../../domain/usecases/mark_prediction_viewed.dart';
import '../../domain/usecases/share_prediction.dart';
import '../../domain/usecases/get_prediction_history.dart';

// State class for predictions
class PredictionState {
  final bool isLoading;
  final PredictionCollection? currentCollection;
  final List<DailyPrediction> history;
  final String? error;
  final DateTime selectedDate;

  const PredictionState({
    this.isLoading = false,
    this.currentCollection,
    this.history = const [],
    this.error,
    required this.selectedDate,
  });

  PredictionState copyWith({
    bool? isLoading,
    PredictionCollection? currentCollection,
    List<DailyPrediction>? history,
    String? error,
    DateTime? selectedDate,
  }) {
    return PredictionState(
      isLoading: isLoading ?? this.isLoading,
      currentCollection: currentCollection ?? this.currentCollection,
      history: history ?? this.history,
      error: error,
      selectedDate: selectedDate ?? this.selectedDate,
    );
  }
}

// Notifier class
class PredictionNotifier extends StateNotifier<PredictionState> {
  final GenerateDailyPredictions _generatePredictions;
  final GetDailyPredictions _getDailyPredictions;
  final MarkPredictionViewed _markViewed;
  final SharePrediction _sharePrediction;
  final GetPredictionHistory _getPredictionHistory;

  PredictionNotifier({
    required GenerateDailyPredictions generatePredictions,
    required GetDailyPredictions getDailyPredictions,
    required MarkPredictionViewed markViewed,
    required SharePrediction sharePrediction,
    required GetPredictionHistory getPredictionHistory,
  })  : _generatePredictions = generatePredictions,
        _getDailyPredictions = getDailyPredictions,
        _markViewed = markViewed,
        _sharePrediction = sharePrediction,
        _getPredictionHistory = getPredictionHistory,
        super(PredictionState(selectedDate: DateTime.now()));

  // Load daily predictions for a specific date
  Future<void> loadDailyPredictions(DateTime date) async {
    state = state.copyWith(isLoading: true, error: null, selectedDate: date);

    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      state = state.copyWith(
        isLoading: false,
        error: 'User not authenticated',
      );
      return;
    }

    final result = await _getDailyPredictions(
      GetDailyPredictionsParams(userId: user.id, date: date),
    );

    result.fold(
      (failure) => state = state.copyWith(
        isLoading: false,
        error: failure.message,
      ),
      (collection) => state = state.copyWith(
        isLoading: false,
        currentCollection: collection,
        error: null,
      ),
    );
  }

  // Generate new predictions for the selected date
  Future<void> generatePredictions({
    List<PredictionType>? types,
    DateTime? date,
  }) async {
    final targetDate = date ?? state.selectedDate;
    final predictionTypes = types ?? PredictionType.values;

    state = state.copyWith(isLoading: true, error: null);

    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      state = state.copyWith(
        isLoading: false,
        error: 'User not authenticated',
      );
      return;
    }

    final result = await _generatePredictions(
      GeneratePredictionsParams(
        userId: user.id,
        date: targetDate,
        types: predictionTypes,
      ),
    );

    result.fold(
      (failure) => state = state.copyWith(
        isLoading: false,
        error: failure.message,
      ),
      (collection) => state = state.copyWith(
        isLoading: false,
        currentCollection: collection,
        error: null,
      ),
    );
  }

  // Mark a prediction as viewed
  Future<void> markAsViewed(String predictionId) async {
    final result = await _markViewed(
      MarkPredictionViewedParams(predictionId: predictionId),
    );

    result.fold(
      (failure) {
        // Handle error silently or show a toast
        print('Failed to mark prediction as viewed: ${failure.message}');
      },
      (_) {
        // Update the current collection to mark the prediction as viewed
        if (state.currentCollection != null) {
          final updatedPredictions = state.currentCollection!.predictions.map((p) {
            if (p.id == predictionId) {
              return p.copyWith(viewedAt: DateTime.now());
            }
            return p;
          }).toList();

          final updatedCollection = state.currentCollection!.copyWith(
            predictions: updatedPredictions,
          );

          state = state.copyWith(currentCollection: updatedCollection);
        }
      },
    );
  }

  // Share a prediction
  Future<void> sharePrediction(String predictionId, String shareMethod) async {
    final result = await _sharePrediction(
      SharePredictionParams(
        predictionId: predictionId,
        shareMethod: shareMethod,
      ),
    );

    result.fold(
      (failure) {
        state = state.copyWith(error: 'Failed to share prediction: ${failure.message}');
      },
      (_) {
        // Update the prediction as shared
        if (state.currentCollection != null) {
          final updatedPredictions = state.currentCollection!.predictions.map((p) {
            if (p.id == predictionId) {
              return p.copyWith(isShared: true);
            }
            return p;
          }).toList();

          final updatedCollection = state.currentCollection!.copyWith(
            predictions: updatedPredictions,
          );

          state = state.copyWith(currentCollection: updatedCollection);
        }
      },
    );
  }

  // Load prediction history
  Future<void> loadPredictionHistory({int limit = 30}) async {
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) return;

    final result = await _getPredictionHistory(
      GetPredictionHistoryParams(userId: user.id, limit: limit),
    );

    result.fold(
      (failure) => state = state.copyWith(error: failure.message),
      (history) => state = state.copyWith(history: history),
    );
  }

  // Change selected date
  void changeDate(DateTime date) {
    state = state.copyWith(selectedDate: date);
    loadDailyPredictions(date);
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Refresh current predictions
  Future<void> refresh() async {
    await loadDailyPredictions(state.selectedDate);
  }
}

// Provider
final predictionNotifierProvider = StateNotifierProvider<PredictionNotifier, PredictionState>((ref) {
  // These dependencies would be injected through the dependency injection container
  throw UnimplementedError('PredictionNotifier provider must be overridden with proper dependencies');
});

// Providers for individual use cases (to be configured in the DI container)
final generateDailyPredictionsProvider = Provider<GenerateDailyPredictions>((ref) {
  throw UnimplementedError('Must be configured in DI container');
});

final getDailyPredictionsProvider = Provider<GetDailyPredictions>((ref) {
  throw UnimplementedError('Must be configured in DI container');
});

final markPredictionViewedProvider = Provider<MarkPredictionViewed>((ref) {
  throw UnimplementedError('Must be configured in DI container');
});

final sharePredictionProvider = Provider<SharePrediction>((ref) {
  throw UnimplementedError('Must be configured in DI container');
});

final getPredictionHistoryProvider = Provider<GetPredictionHistory>((ref) {
  throw UnimplementedError('Must be configured in DI container');
});

// Helper providers
final currentUserIdProvider = Provider<String?>((ref) {
  return Supabase.instance.client.auth.currentUser?.id;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  return Supabase.instance.client.auth.currentUser != null;
});

// Convenience providers for specific data
final currentPredictionCollectionProvider = Provider<PredictionCollection?>((ref) {
  final state = ref.watch(predictionNotifierProvider);
  return state.currentCollection;
});

final predictionHistoryProvider = Provider<List<DailyPrediction>>((ref) {
  final state = ref.watch(predictionNotifierProvider);
  return state.history;
});

final isPredictionsLoadingProvider = Provider<bool>((ref) {
  final state = ref.watch(predictionNotifierProvider);
  return state.isLoading;
});

final predictionErrorProvider = Provider<String?>((ref) {
  final state = ref.watch(predictionNotifierProvider);
  return state.error;
});
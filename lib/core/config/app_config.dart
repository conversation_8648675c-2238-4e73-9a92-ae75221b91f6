import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  static String get supabaseUrl => dotenv.env['SUPABASE_URL'] ?? '';
  static String get supabaseAnonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';
  static String get supabaseServiceRoleKey => dotenv.env['SUPABASE_SERVICE_ROLE_KEY'] ?? '';
  
  static String get openaiApiKey => dotenv.env['OPENAI_API_KEY'] ?? '';
  static String get openaiOrganizationId => dotenv.env['OPENAI_ORGANIZATION_ID'] ?? '';
  
  static String get claudeApiKey => dotenv.env['CLAUDE_API_KEY'] ?? '';
  
  static String get googlePlacesApiKey => dotenv.env['GOOGLE_PLACES_API_KEY'] ?? '';
  static String get googleGeocodingApiKey => dotenv.env['GOOGLE_GEOCODING_API_KEY'] ?? '';
  
  static String get appEnv => dotenv.env['APP_ENV'] ?? 'development';
  static bool get isDebugMode => dotenv.env['DEBUG_MODE'] == 'true';
  static String get logLevel => dotenv.env['LOG_LEVEL'] ?? 'info';
  
  static bool get isProduction => appEnv == 'production';
  static bool get isDevelopment => appEnv == 'development';
  static bool get isStaging => appEnv == 'staging';
  
  // Validation method
  static bool validateConfig() {
    final requiredKeys = [
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY',
    ];
    
    for (final key in requiredKeys) {
      if (dotenv.env[key] == null || dotenv.env[key]!.isEmpty) {
        throw Exception('Missing required environment variable: $key');
      }
    }
    
    return true;
  }
  
  // AI Configuration
  static bool get hasOpenAI => openaiApiKey.isNotEmpty;
  static bool get hasClaude => claudeApiKey.isNotEmpty;
  static bool get hasAnyAI => hasOpenAI || hasClaude;
  
  // Feature flags
  static bool get enablePredictions => hasAnyAI;
  static bool get enableLocationServices => googlePlacesApiKey.isNotEmpty;
  static bool get enableAnalytics => !isDevelopment;
}
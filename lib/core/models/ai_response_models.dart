import 'package:equatable/equatable.dart';
import '../../features/predictions/domain/entities/prediction.dart';

/// Response model for AI prediction generation
class AIPredictionResponse extends Equatable {
  final String title;
  final String content;
  final String summary;
  final List<String> keywords;
  final PredictionMood mood;
  final int energyLevel;
  final List<String> luckyNumbers;
  final List<String> luckyColors;
  final String advice;
  final String warning;

  const AIPredictionResponse({
    required this.title,
    required this.content,
    required this.summary,
    required this.keywords,
    required this.mood,
    required this.energyLevel,
    required this.luckyNumbers,
    required this.luckyColors,
    required this.advice,
    required this.warning,
  });

  @override
  List<Object> get props => [
        title,
        content,
        summary,
        keywords,
        mood,
        energyLevel,
        luckyNumbers,
        luckyColors,
        advice,
        warning,
      ];
}

/// Response model for AI summary generation
class AISummaryResponse extends Equatable {
  final PredictionMood overallMood;
  final int overallEnergyLevel;
  final String dailyTheme;
  final List<String> topKeywords;
  final String mainAdvice;
  final String primaryFocus;
  final List<String> avoidActions;
  final List<String> favorableActions;

  const AISummaryResponse({
    required this.overallMood,
    required this.overallEnergyLevel,
    required this.dailyTheme,
    required this.topKeywords,
    required this.mainAdvice,
    required this.primaryFocus,
    required this.avoidActions,
    required this.favorableActions,
  });

  @override
  List<Object> get props => [
        overallMood,
        overallEnergyLevel,
        dailyTheme,
        topKeywords,
        mainAdvice,
        primaryFocus,
        avoidActions,
        favorableActions,
      ];
}

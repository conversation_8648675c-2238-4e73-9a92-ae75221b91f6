import 'package:flutter/material.dart';
import '../design_system.dart';

class DesignSystemShowcase extends StatelessWidget {
  const DesignSystemShowcase({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Design System Showcase',
          style: AppTextStyles.headlineMedium,
        ),
        centerTitle: true,
        backgroundColor: AppColors.cosmicDark800,
      ),
      backgroundColor: AppColors.cosmicDark800,
      body: SingleChildScrollView(
        padding: AppSpacing.screenPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection('Colors', _buildColorShowcase()),
            _buildSection('Typography', _buildTypographyShowcase()),
            _buildSection('Buttons', _buildButtonShowcase()),
            _buildSection('Cards', _buildCardShowcase()),
            _buildSection('Inputs', _buildInputShowcase()),
            _buildSection('Mystical Components', _buildMysticalShowcase()),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.cosmicGold400,
          ),
        ),
        AppSpacing.verticalSpacingMD,
        content,
        AppSpacing.verticalSpacingXXL,
      ],
    );
  }

  Widget _buildColorShowcase() {
    return Column(
      children: [
        // Primary Colors
        _buildColorRow('Primary Purple', [
          AppColors.mysticPurple100,
          AppColors.mysticPurple300,
          AppColors.mysticPurple500,
          AppColors.mysticPurple700,
          AppColors.mysticPurple900,
        ]),
        
        // Secondary Colors
        _buildColorRow('Cosmic Gold', [
          AppColors.cosmicGold100,
          AppColors.cosmicGold300,
          AppColors.cosmicGold400,
          AppColors.cosmicGold600,
          AppColors.cosmicGold800,
        ]),
        
        // Accent Colors
        _buildColorRow('Mystical Rose', [
          AppColors.mysticalRose100,
          AppColors.mysticalRose300,
          AppColors.mysticalRose400,
          AppColors.mysticalRose600,
          AppColors.mysticalRose800,
        ]),
        
        // Semantic Colors
        _buildColorRow('Semantic', [
          AppColors.success500,
          AppColors.warning500,
          AppColors.error500,
          AppColors.info500,
        ]),
      ],
    );
  }

  Widget _buildColorRow(String name, List<Color> colors) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: AppTextStyles.titleMedium,
          ),
          AppSpacing.verticalSpacingXS,
          Row(
            children: colors.map((color) => _buildColorSwatch(color)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildColorSwatch(Color color) {
    return Expanded(
      child: Container(
        height: 60,
        margin: const EdgeInsets.only(right: AppSpacing.xs),
        decoration: BoxDecoration(
          color: color,
          borderRadius: AppSpacing.borderRadiusSM,
          border: Border.all(color: AppColors.borderPrimary),
        ),
      ),
    );
  }

  Widget _buildTypographyShowcase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTypographyItem('Display Large', AppTextStyles.displayLarge),
        _buildTypographyItem('Headline Large', AppTextStyles.headlineLarge),
        _buildTypographyItem('Title Large', AppTextStyles.titleLarge),
        _buildTypographyItem('Body Large', AppTextStyles.bodyLarge),
        _buildTypographyItem('Label Large', AppTextStyles.labelLarge),
        _buildTypographyItem('Mystic Title', AppTextStyles.mysticTitle),
        _buildTypographyItem('Mono Large', AppTextStyles.monoLarge),
      ],
    );
  }

  Widget _buildTypographyItem(String name, TextStyle style) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          AppSpacing.verticalSpacingXXS,
          Text(
            'The quick brown fox jumps over the lazy dog',
            style: style,
          ),
        ],
      ),
    );
  }

  Widget _buildButtonShowcase() {
    return Wrap(
      spacing: AppSpacing.md,
      runSpacing: AppSpacing.md,
      children: [
        AppButton.primary(
          text: 'Primary',
          onPressed: () {},
        ),
        AppButton.secondary(
          text: 'Secondary',
          onPressed: () {},
        ),
        AppButton.mystic(
          text: 'Mystic',
          onPressed: () {},
        ),
        AppButton.ghost(
          text: 'Ghost',
          onPressed: () {},
        ),
        AppButton.destructive(
          text: 'Destructive',
          onPressed: () {},
        ),
        AppButton.primary(
          text: 'Loading',
          isLoading: true,
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildCardShowcase() {
    return Column(
      children: [
        AppCard.basic(
          child: Text(
            'Basic Card',
            style: AppTextStyles.titleMedium,
          ),
        ),
        AppSpacing.verticalSpacingMD,
        AppCard.elevated(
          child: Text(
            'Elevated Card',
            style: AppTextStyles.titleMedium,
          ),
        ),
        AppSpacing.verticalSpacingMD,
        AppCard.mystic(
          child: Text(
            'Mystic Card with special styling and gradients',
            style: AppTextStyles.titleMedium,
          ),
        ),
        AppSpacing.verticalSpacingMD,
        MysticalCard(
          title: 'Mystical Card',
          subtitle: 'With header and icon',
          icon: const Icon(
            Icons.auto_awesome,
            color: AppColors.cosmicGold400,
          ),
          child: Text(
            'This is a pre-styled mystical card component with built-in header, icon, and consistent styling.',
            style: AppTextStyles.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildInputShowcase() {
    return Column(
      children: [
        AppInput(
          label: 'Basic Input',
          hint: 'Enter your text here',
          helperText: 'This is helper text',
        ),
        AppSpacing.verticalSpacingMD,
        AppInput.email(
          label: 'Email Input',
          hint: 'Enter your email',
          prefixIcon: const Icon(Icons.email_outlined),
        ),
        AppSpacing.verticalSpacingMD,
        AppInput.password(
          label: 'Password Input',
          hint: 'Enter your password',
        ),
        AppSpacing.verticalSpacingMD,
        AppInput.search(
          hint: 'Search...',
        ),
      ],
    );
  }

  Widget _buildMysticalShowcase() {
    return Column(
      children: [
        // Tarot Card
        Row(
          children: [
            TarotCard(
              cardName: 'The Star',
              cardDescription: 'Hope & Renewal',
            ),
            AppSpacing.horizontalSpacingMD,
            TarotCard(
              cardName: 'The Moon',
              cardDescription: 'Illusion & Intuition',
              isReversed: true,
              isSelected: true,
            ),
          ],
        ),
        
        AppSpacing.verticalSpacingXL,
        
        // System Cards
        SystemCard(
          systemName: 'Tarot Reading',
          description: 'Discover insights through ancient wisdom',
          icon: Icons.style,
          accentColor: AppColors.tarotPurple,
          isPremium: true,
        ),
        
        AppSpacing.verticalSpacingMD,
        
        SystemCard(
          systemName: 'Astrology',
          description: 'Celestial guidance for your journey',
          icon: Icons.nights_stay,
          accentColor: AppColors.astrologyBlue,
        ),
        
        AppSpacing.verticalSpacingXL,
        
        // Other Mystical Components
        Row(
          children: [
            Expanded(
              child: RuneSymbol(
                runeName: 'Fehu',
                runeSymbol: 'ᚠ',
                meaning: 'Wealth',
                isSelected: true,
              ),
            ),
            AppSpacing.horizontalSpacingMD,
            Expanded(
              child: NumerologyNumber(
                number: 7,
                meaning: 'Spirituality',
                description: 'Introspection & Wisdom',
                isLucky: true,
              ),
            ),
          ],
        ),
        
        AppSpacing.verticalSpacingMD,
        
        CrystalCard(
          crystalName: 'Amethyst',
          properties: 'Enhances intuition and spiritual awareness. Promotes calm and clarity.',
          crystalColor: AppColors.crystalPink,
        ),
      ],
    );
  }
}
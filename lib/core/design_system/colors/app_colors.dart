import 'package:flutter/material.dart';

class AppColors {
  // ===== PRIMARY PALETTE =====
  static const Color mysticPurple50 = Color(0xFFF3F0FF);
  static const Color mysticPurple100 = Color(0xFFE5DFFF);
  static const Color mysticPurple200 = Color(0xFFCCC2FF);
  static const Color mysticPurple300 = Color(0xFFAA9AFF);
  static const Color mysticPurple400 = Color(0xFF8B6EFF);
  static const Color mysticPurple500 = Color(0xFF6A4C93); // Primary
  static const Color mysticPurple600 = Color(0xFF5A3D7A);
  static const Color mysticPurple700 = Color(0xFF4A2E62);
  static const Color mysticPurple800 = Color(0xFF3B1F4A);
  static const Color mysticPurple900 = Color(0xFF2C1532);

  // ===== SECONDARY PALETTE (Cosmic Gold) =====
  static const Color cosmicGold50 = Color(0xFFFFFAE6);
  static const Color cosmicGold100 = Color(0xFFFFF2CC);
  static const Color cosmicGold200 = Color(0xFFFFE699);
  static const Color cosmicGold300 = Color(0xFFFFD966);
  static const Color cosmicGold400 = Color(0xFFFFCB77); // Secondary
  static const Color cosmicGold500 = Color(0xFFE6B85C);
  static const Color cosmicGold600 = Color(0xFFCC9F41);
  static const Color cosmicGold700 = Color(0xFFB3862A);
  static const Color cosmicGold800 = Color(0xFF996D17);
  static const Color cosmicGold900 = Color(0xFF805408);

  // ===== ACCENT PALETTE (Mystical Rose) =====
  static const Color mysticalRose50 = Color(0xFFFFEEF0);
  static const Color mysticalRose100 = Color(0xFFFFDDE1);
  static const Color mysticalRose200 = Color(0xFFFFBBC3);
  static const Color mysticalRose300 = Color(0xFFFF99A5);
  static const Color mysticalRose400 = Color(0xFFFF6B6B); // Accent
  static const Color mysticalRose500 = Color(0xFFE65555);
  static const Color mysticalRose600 = Color(0xFFCC3F3F);
  static const Color mysticalRose700 = Color(0xFFB32929);
  static const Color mysticalRose800 = Color(0xFF991313);
  static const Color mysticalRose900 = Color(0xFF800000);

  // ===== NEUTRAL PALETTE (Cosmic Dark) =====
  static const Color cosmicDark50 = Color(0xFFF5F5F7);
  static const Color cosmicDark100 = Color(0xFFE8E8EC);
  static const Color cosmicDark200 = Color(0xFFD1D1D9);
  static const Color cosmicDark300 = Color(0xFFB0B0BE);
  static const Color cosmicDark400 = Color(0xFF8F8FA3);
  static const Color cosmicDark500 = Color(0xFF6E6E88);
  static const Color cosmicDark600 = Color(0xFF16213E); // Surface
  static const Color cosmicDark700 = Color(0xFF0F3460); // Card
  static const Color cosmicDark800 = Color(0xFF1A1A2E); // Background
  static const Color cosmicDark900 = Color(0xFF0E0E1A);

  // ===== SEMANTIC COLORS =====
  static const Color success50 = Color(0xFFF0FDF4);
  static const Color success100 = Color(0xFFDCFCE7);
  static const Color success500 = Color(0xFF4ECDC4); // Success
  static const Color success600 = Color(0xFF3BA99F);
  static const Color success700 = Color(0xFF28857A);

  static const Color warning50 = Color(0xFFFFFBEB);
  static const Color warning100 = Color(0xFFFEF3C7);
  static const Color warning500 = Color(0xFFFFE66D); // Warning
  static const Color warning600 = Color(0xFFE6C954);
  static const Color warning700 = Color(0xFFCCAD3B);

  static const Color error50 = Color(0xFFFEF2F2);
  static const Color error100 = Color(0xFFFECDCA);
  static const Color error500 = Color(0xFFFF6B6B); // Error
  static const Color error600 = Color(0xFFE65555);
  static const Color error700 = Color(0xFFCC3F3F);

  static const Color info50 = Color(0xFFEFF6FF);
  static const Color info100 = Color(0xFFDBEAFE);
  static const Color info500 = Color(0xFF3B82F6); // Info
  static const Color info600 = Color(0xFF2563EB);
  static const Color info700 = Color(0xFF1D4ED8);

  // ===== TEXT COLORS =====
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB0B0B0);
  static const Color textTertiary = Color(0xFF8F8FA3);
  static const Color textDisabled = Color(0xFF6E6E88);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFF1A1A2E);

  // ===== OVERLAY COLORS =====
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  static const Color overlayHeavy = Color(0xB3000000);
  static const Color shimmer = Color(0x33FFFFFF);

  // ===== MYSTICAL SYSTEM COLORS =====
  static const Color tarotPurple = Color(0xFF6A4C93);
  static const Color astrologyBlue = Color(0xFF4A90E2);
  static const Color numerologyGreen = Color(0xFF7ED321);
  static const Color chineseRed = Color(0xFFD0021B);
  static const Color runesGold = Color(0xFFF5A623);
  static const Color crystalPink = Color(0xFFBD10E0);
  static const Color ayurvedaOrange = Color(0xFFFF9500);
  static const Color kabbalaWhite = Color(0xFFFFFFFF);

  // ===== GRADIENTS =====
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [mysticPurple500, mysticPurple700],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [cosmicGold400, cosmicGold600],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [mysticalRose400, mysticalRose600],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [cosmicDark800, cosmicDark600],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [cosmicDark700, cosmicDark600],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient mysticGradient = LinearGradient(
    colors: [mysticPurple500, cosmicGold400, mysticalRose400],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const RadialGradient cosmicRadial = RadialGradient(
    colors: [mysticPurple500, cosmicDark800],
    center: Alignment.center,
    radius: 1.0,
  );

  // ===== SHADOW COLORS =====
  static const Color shadowPrimary = Color(0x26000000);
  static const Color shadowSecondary = Color(0x1A000000);
  static const Color shadowMystic = Color(0x4D6A4C93);
  static const Color shadowGold = Color(0x4DFFCB77);

  // ===== BORDER COLORS =====
  static const Color borderPrimary = Color(0xFF2A2A3E);
  static const Color borderSecondary = Color(0xFF3A3A4E);
  static const Color borderAccent = mysticPurple500;
  static const Color borderSuccess = success500;
  static const Color borderWarning = warning500;
  static const Color borderError = error500;

  // ===== CONVENIENCE ALIASES =====
  static const Color primary50 = mysticPurple50;
  static const Color primary100 = mysticPurple100;
  static const Color primary200 = mysticPurple200;
  static const Color primary300 = mysticPurple300;
  static const Color primary400 = mysticPurple400;
  static const Color primary500 = mysticPurple500;
  static const Color primary600 = mysticPurple600;
  static const Color primary700 = mysticPurple700;
  static const Color primary800 = mysticPurple800;
  static const Color primary900 = mysticPurple900;

  static const Color secondary50 = cosmicGold50;
  static const Color secondary100 = cosmicGold100;
  static const Color secondary200 = cosmicGold200;
  static const Color secondary300 = cosmicGold300;
  static const Color secondary400 = cosmicGold400;
  static const Color secondary500 = cosmicGold500;
  static const Color secondary600 = cosmicGold600;
  static const Color secondary700 = cosmicGold700;
  static const Color secondary800 = cosmicGold800;
  static const Color secondary900 = cosmicGold900;

  static const Color dark50 = cosmicDark50;
  static const Color dark100 = cosmicDark100;
  static const Color dark200 = cosmicDark200;
  static const Color dark300 = cosmicDark300;
  static const Color dark400 = cosmicDark400;
  static const Color dark500 = cosmicDark500;
  static const Color dark600 = cosmicDark600;
  static const Color dark700 = cosmicDark700;
  static const Color dark800 = cosmicDark800;
  static const Color dark900 = cosmicDark900;

  // ===== HELPER METHODS =====
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  // ===== SYSTEM SPECIFIC COLOR MAPS =====
  static const Map<String, Color> mysticalSystemColors = {
    'tarot': tarotPurple,
    'astrology': astrologyBlue,
    'numerology': numerologyGreen,
    'chinese': chineseRed,
    'runes': runesGold,
    'crystals': crystalPink,
    'ayurveda': ayurvedaOrange,
    'kabbalah': kabbalaWhite,
    'iching': cosmicGold500,
    'biorhythms': success500,
    'lunar': mysticPurple400,
    'bach_flowers': mysticalRose400,
    'synchronicities': cosmicGold400,
  };
}
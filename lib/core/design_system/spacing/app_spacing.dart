import 'package:flutter/material.dart';

class AppSpacing {
  // ===== BASE SPACING UNIT =====
  static const double baseUnit = 4.0;

  // ===== SPACING SCALE =====
  static const double xxs = baseUnit * 1; // 4px
  static const double xs = baseUnit * 2; // 8px
  static const double sm = baseUnit * 3; // 12px
  static const double md = baseUnit * 4; // 16px
  static const double lg = baseUnit * 5; // 20px
  static const double xl = baseUnit * 6; // 24px
  static const double xxl = baseUnit * 8; // 32px
  static const double xxxl = baseUnit * 10; // 40px
  static const double huge = baseUnit * 12; // 48px
  static const double massive = baseUnit * 16; // 64px
  static const double giant = baseUnit * 20; // 80px

  // ===== PADDING PRESETS =====
  static const EdgeInsets paddingXXS = EdgeInsets.all(xxs);
  static const EdgeInsets paddingXS = EdgeInsets.all(xs);
  static const EdgeInsets paddingSM = EdgeInsets.all(sm);
  static const EdgeInsets paddingMD = EdgeInsets.all(md);
  static const EdgeInsets paddingLG = EdgeInsets.all(lg);
  static const EdgeInsets paddingXL = EdgeInsets.all(xl);
  static const EdgeInsets paddingXXL = EdgeInsets.all(xxl);
  static const EdgeInsets paddingXXXL = EdgeInsets.all(xxxl);

  // ===== HORIZONTAL PADDING =====
  static const EdgeInsets horizontalXXS = EdgeInsets.symmetric(horizontal: xxs);
  static const EdgeInsets horizontalXS = EdgeInsets.symmetric(horizontal: xs);
  static const EdgeInsets horizontalSM = EdgeInsets.symmetric(horizontal: sm);
  static const EdgeInsets horizontalMD = EdgeInsets.symmetric(horizontal: md);
  static const EdgeInsets horizontalLG = EdgeInsets.symmetric(horizontal: lg);
  static const EdgeInsets horizontalXL = EdgeInsets.symmetric(horizontal: xl);
  static const EdgeInsets horizontalXXL = EdgeInsets.symmetric(horizontal: xxl);
  static const EdgeInsets horizontalXXXL = EdgeInsets.symmetric(horizontal: xxxl);

  // ===== VERTICAL PADDING =====
  static const EdgeInsets verticalXXS = EdgeInsets.symmetric(vertical: xxs);
  static const EdgeInsets verticalXS = EdgeInsets.symmetric(vertical: xs);
  static const EdgeInsets verticalSM = EdgeInsets.symmetric(vertical: sm);
  static const EdgeInsets verticalMD = EdgeInsets.symmetric(vertical: md);
  static const EdgeInsets verticalLG = EdgeInsets.symmetric(vertical: lg);
  static const EdgeInsets verticalXL = EdgeInsets.symmetric(vertical: xl);
  static const EdgeInsets verticalXXL = EdgeInsets.symmetric(vertical: xxl);
  static const EdgeInsets verticalXXXL = EdgeInsets.symmetric(vertical: xxxl);

  // ===== SPECIFIC COMPONENT PADDING =====
  static const EdgeInsets cardPadding = EdgeInsets.all(md);
  static const EdgeInsets cardPaddingLarge = EdgeInsets.all(xl);
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(horizontal: xl, vertical: sm);
  static const EdgeInsets buttonPaddingLarge = EdgeInsets.symmetric(horizontal: xxl, vertical: md);
  static const EdgeInsets inputPadding = EdgeInsets.symmetric(horizontal: md, vertical: sm);
  static const EdgeInsets screenPadding = EdgeInsets.all(xl);
  static const EdgeInsets sectionPadding = EdgeInsets.symmetric(vertical: xxl);
  static const EdgeInsets listItemPadding = EdgeInsets.symmetric(horizontal: md, vertical: sm);

  // ===== MYSTICAL COMPONENT SPECIFIC =====
  static const EdgeInsets tarotCardPadding = EdgeInsets.all(md);
  static const EdgeInsets predictionCardPadding = EdgeInsets.all(lg);
  static const EdgeInsets systemCardPadding = EdgeInsets.all(md);
  static const EdgeInsets mysticModalPadding = EdgeInsets.all(xl);

  // ===== MARGIN PRESETS =====
  static const EdgeInsets marginXXS = EdgeInsets.all(xxs);
  static const EdgeInsets marginXS = EdgeInsets.all(xs);
  static const EdgeInsets marginSM = EdgeInsets.all(sm);
  static const EdgeInsets marginMD = EdgeInsets.all(md);
  static const EdgeInsets marginLG = EdgeInsets.all(lg);
  static const EdgeInsets marginXL = EdgeInsets.all(xl);
  static const EdgeInsets marginXXL = EdgeInsets.all(xxl);

  // ===== SIZEDBOX PRESETS =====
  static const SizedBox spacingXXS = SizedBox(height: xxs, width: xxs);
  static const SizedBox spacingXS = SizedBox(height: xs, width: xs);
  static const SizedBox spacingSM = SizedBox(height: sm, width: sm);
  static const SizedBox spacingMD = SizedBox(height: md, width: md);
  static const SizedBox spacingLG = SizedBox(height: lg, width: lg);
  static const SizedBox spacingXL = SizedBox(height: xl, width: xl);
  static const SizedBox spacingXXL = SizedBox(height: xxl, width: xxl);
  static const SizedBox spacingXXXL = SizedBox(height: xxxl, width: xxxl);
  static const SizedBox spacingHuge = SizedBox(height: huge, width: huge);

  // ===== VERTICAL SPACING =====
  static const SizedBox verticalSpacingXXS = SizedBox(height: xxs);
  static const SizedBox verticalSpacingXS = SizedBox(height: xs);
  static const SizedBox verticalSpacingSM = SizedBox(height: sm);
  static const SizedBox verticalSpacingMD = SizedBox(height: md);
  static const SizedBox verticalSpacingLG = SizedBox(height: lg);
  static const SizedBox verticalSpacingXL = SizedBox(height: xl);
  static const SizedBox verticalSpacingXXL = SizedBox(height: xxl);
  static const SizedBox verticalSpacingXXXL = SizedBox(height: xxxl);
  static const SizedBox verticalSpacingHuge = SizedBox(height: huge);

  // ===== HORIZONTAL SPACING =====
  static const SizedBox horizontalSpacingXXS = SizedBox(width: xxs);
  static const SizedBox horizontalSpacingXS = SizedBox(width: xs);
  static const SizedBox horizontalSpacingSM = SizedBox(width: sm);
  static const SizedBox horizontalSpacingMD = SizedBox(width: md);
  static const SizedBox horizontalSpacingLG = SizedBox(width: lg);
  static const SizedBox horizontalSpacingXL = SizedBox(width: xl);
  static const SizedBox horizontalSpacingXXL = SizedBox(width: xxl);
  static const SizedBox horizontalSpacingXXXL = SizedBox(width: xxxl);
  static const SizedBox horizontalSpacingHuge = SizedBox(width: huge);

  // ===== BORDER RADIUS =====
  static const double radiusXS = baseUnit * 1; // 4px
  static const double radiusSM = baseUnit * 2; // 8px
  static const double radiusMD = baseUnit * 3; // 12px
  static const double radiusLG = baseUnit * 4; // 16px
  static const double radiusXL = baseUnit * 5; // 20px
  static const double radiusXXL = baseUnit * 6; // 24px
  static const double radiusCircular = 999; // Circular

  // ===== BORDER RADIUS PRESETS =====
  static const BorderRadius borderRadiusXS = BorderRadius.all(Radius.circular(radiusXS));
  static const BorderRadius borderRadiusSM = BorderRadius.all(Radius.circular(radiusSM));
  static const BorderRadius borderRadiusMD = BorderRadius.all(Radius.circular(radiusMD));
  static const BorderRadius borderRadiusLG = BorderRadius.all(Radius.circular(radiusLG));
  static const BorderRadius borderRadiusXL = BorderRadius.all(Radius.circular(radiusXL));
  static const BorderRadius borderRadiusXXL = BorderRadius.all(Radius.circular(radiusXXL));
  static const BorderRadius borderRadiusCircular = BorderRadius.all(Radius.circular(radiusCircular));

  // ===== COMPONENT SPECIFIC BORDER RADIUS =====
  static const BorderRadius cardRadius = borderRadiusLG;
  static const BorderRadius buttonRadius = borderRadiusMD;
  static const BorderRadius inputRadius = borderRadiusMD;
  static const BorderRadius modalRadius = borderRadiusXL;
  static const BorderRadius bottomSheetRadius = BorderRadius.only(
    topLeft: Radius.circular(radiusXL),
    topRight: Radius.circular(radiusXL),
  );

  // ===== MYSTICAL COMPONENT BORDER RADIUS =====
  static const BorderRadius tarotCardRadius = borderRadiusLG;
  static const BorderRadius predictionCardRadius = borderRadiusXL;
  static const BorderRadius systemCardRadius = borderRadiusMD;
  static const BorderRadius mysticModalRadius = borderRadiusXXL;

  // ===== DIVIDER SPACING =====
  static const double dividerHeight = 1.0;
  static const double dividerIndent = md;
  static const double dividerEndIndent = md;

  // ===== ICON SIZES =====
  static const double iconXS = 12.0;
  static const double iconSM = 16.0;
  static const double iconMD = 24.0;
  static const double iconLG = 32.0;
  static const double iconXL = 40.0;
  static const double iconXXL = 48.0;
  static const double iconHuge = 64.0;
  static const double iconMassive = 80.0;

  // ===== AVATAR SIZES =====
  static const double avatarXS = 24.0;
  static const double avatarSM = 32.0;
  static const double avatarMD = 40.0;
  static const double avatarLG = 56.0;
  static const double avatarXL = 80.0;
  static const double avatarXXL = 120.0;

  // ===== BUTTON HEIGHTS =====
  static const double buttonHeightSM = 32.0;
  static const double buttonHeightMD = 40.0;
  static const double buttonHeightLG = 48.0;
  static const double buttonHeightXL = 56.0;

  // ===== INPUT HEIGHTS =====
  static const double inputHeightSM = 32.0;
  static const double inputHeightMD = 40.0;
  static const double inputHeightLG = 48.0;
  static const double inputHeightXL = 56.0;

  // ===== CARD DIMENSIONS =====
  static const double cardMinHeight = 120.0;
  static const double cardMaxHeight = 400.0;
  static const double cardDefaultHeight = 200.0;

  // ===== MYSTICAL CARD DIMENSIONS =====
  static const double tarotCardWidth = 140.0;
  static const double tarotCardHeight = 240.0;
  static const double predictionCardMinHeight = 160.0;
  static const double systemCardHeight = 120.0;

  // ===== BREAKPOINTS =====
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  // ===== HELPER METHODS =====
  static EdgeInsets symmetric({double? horizontal, double? vertical}) {
    return EdgeInsets.symmetric(
      horizontal: horizontal ?? 0,
      vertical: vertical ?? 0,
    );
  }

  static EdgeInsets only({
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return EdgeInsets.only(
      left: left ?? 0,
      top: top ?? 0,
      right: right ?? 0,
      bottom: bottom ?? 0,
    );
  }

  static SizedBox verticalSpace(double height) {
    return SizedBox(height: height);
  }

  static SizedBox horizontalSpace(double width) {
    return SizedBox(width: width);
  }

  static BorderRadius circular(double radius) {
    return BorderRadius.circular(radius);
  }

  static BorderRadius topOnly(double radius) {
    return BorderRadius.only(
      topLeft: Radius.circular(radius),
      topRight: Radius.circular(radius),
    );
  }

  static BorderRadius bottomOnly(double radius) {
    return BorderRadius.only(
      bottomLeft: Radius.circular(radius),
      bottomRight: Radius.circular(radius),
    );
  }
}
import 'package:flutter/material.dart';
import '../colors/app_colors.dart';

class AppTextStyles {
  // ===== FONT FAMILIES =====
  static const String primaryFont = 'SF Pro Display'; // iOS/macOS system font
  static const String secondaryFont = 'Roboto'; // Android system font
  static const String decorativeFont = 'Playfair Display'; // For mystical headings
  static const String monospaceFont = 'SF Mono'; // For numbers/codes

  // ===== FONT WEIGHTS =====
  static const FontWeight thin = FontWeight.w100;
  static const FontWeight extraLight = FontWeight.w200;
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight black = FontWeight.w900;

  // ===== DISPLAY STYLES =====
  static const TextStyle displayLarge = TextStyle(
    fontSize: 48,
    fontWeight: bold,
    letterSpacing: -1.5,
    color: AppColors.textPrimary,
    height: 1.1,
  );

  static const TextStyle displayMedium = TextStyle(
    fontSize: 40,
    fontWeight: bold,
    letterSpacing: -1.0,
    color: AppColors.textPrimary,
    height: 1.15,
  );

  static const TextStyle displaySmall = TextStyle(
    fontSize: 32,
    fontWeight: semiBold,
    letterSpacing: -0.8,
    color: AppColors.textPrimary,
    height: 1.2,
  );

  // ===== HEADLINE STYLES =====
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 28,
    fontWeight: semiBold,
    letterSpacing: -0.5,
    color: AppColors.textPrimary,
    height: 1.25,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontSize: 24,
    fontWeight: semiBold,
    letterSpacing: -0.3,
    color: AppColors.textPrimary,
    height: 1.3,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 20,
    fontWeight: medium,
    letterSpacing: -0.2,
    color: AppColors.textPrimary,
    height: 1.35,
  );

  // ===== TITLE STYLES =====
  static const TextStyle titleLarge = TextStyle(
    fontSize: 18,
    fontWeight: medium,
    letterSpacing: 0.0,
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle titleMedium = TextStyle(
    fontSize: 16,
    fontWeight: medium,
    letterSpacing: 0.1,
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle titleSmall = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.2,
    color: AppColors.textPrimary,
    height: 1.45,
  );

  // ===== BODY STYLES =====
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.1,
    color: AppColors.textPrimary,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: regular,
    letterSpacing: 0.2,
    color: AppColors.textSecondary,
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.3,
    color: AppColors.textSecondary,
    height: 1.5,
  );

  // ===== LABEL STYLES =====
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.5,
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.6,
    color: AppColors.textSecondary,
    height: 1.4,
  );

  static const TextStyle labelSmall = TextStyle(
    fontSize: 10,
    fontWeight: medium,
    letterSpacing: 0.8,
    color: AppColors.textTertiary,
    height: 1.4,
  );

  // ===== DECORATIVE STYLES (For Mystical Content) =====
  static const TextStyle mysticTitle = TextStyle(
    fontFamily: decorativeFont,
    fontSize: 32,
    fontWeight: bold,
    letterSpacing: -0.5,
    color: AppColors.cosmicGold400,
    height: 1.2,
    shadows: [
      Shadow(
        color: AppColors.shadowMystic,
        offset: Offset(0, 2),
        blurRadius: 4,
      ),
    ],
  );

  static const TextStyle mysticSubtitle = TextStyle(
    fontFamily: decorativeFont,
    fontSize: 24,
    fontWeight: semiBold,
    letterSpacing: -0.3,
    color: AppColors.mysticPurple300,
    height: 1.25,
  );

  static const TextStyle mysticBody = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.3,
    color: AppColors.textPrimary,
    height: 1.6,
    fontStyle: FontStyle.italic,
  );

  // ===== BUTTON STYLES =====
  static const TextStyle buttonLarge = TextStyle(
    fontSize: 16,
    fontWeight: semiBold,
    letterSpacing: 0.5,
    color: AppColors.textOnPrimary,
    height: 1.2,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontSize: 14,
    fontWeight: semiBold,
    letterSpacing: 0.5,
    color: AppColors.textOnPrimary,
    height: 1.2,
  );

  static const TextStyle buttonSmall = TextStyle(
    fontSize: 12,
    fontWeight: semiBold,
    letterSpacing: 0.6,
    color: AppColors.textOnPrimary,
    height: 1.2,
  );

  // ===== MONOSPACE STYLES (For Numbers/Codes) =====
  static const TextStyle monoLarge = TextStyle(
    fontFamily: monospaceFont,
    fontSize: 20,
    fontWeight: medium,
    letterSpacing: 0.0,
    color: AppColors.cosmicGold400,
    height: 1.3,
  );

  static const TextStyle monoMedium = TextStyle(
    fontFamily: monospaceFont,
    fontSize: 16,
    fontWeight: medium,
    letterSpacing: 0.0,
    color: AppColors.cosmicGold400,
    height: 1.3,
  );

  static const TextStyle monoSmall = TextStyle(
    fontFamily: monospaceFont,
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.0,
    color: AppColors.cosmicGold400,
    height: 1.3,
  );

  // ===== CAPTION STYLES =====
  static const TextStyle captionLarge = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
    color: AppColors.textTertiary,
    height: 1.4,
  );

  static const TextStyle captionMedium = TextStyle(
    fontSize: 11,
    fontWeight: regular,
    letterSpacing: 0.5,
    color: AppColors.textTertiary,
    height: 1.4,
  );

  static const TextStyle captionSmall = TextStyle(
    fontSize: 10,
    fontWeight: regular,
    letterSpacing: 0.6,
    color: AppColors.textDisabled,
    height: 1.4,
  );

  // ===== OVERLINE STYLES =====
  static const TextStyle overlineLarge = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 1.5,
    color: AppColors.textSecondary,
    height: 1.2,
  );

  static const TextStyle overlineMedium = TextStyle(
    fontSize: 11,
    fontWeight: medium,
    letterSpacing: 1.5,
    color: AppColors.textTertiary,
    height: 1.2,
  );

  static const TextStyle overlineSmall = TextStyle(
    fontSize: 10,
    fontWeight: medium,
    letterSpacing: 2.0,
    color: AppColors.textDisabled,
    height: 1.2,
  );

  // ===== SEMANTIC STYLES =====
  static const TextStyle successText = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.success500,
    height: 1.4,
  );

  static const TextStyle warningText = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.warning500,
    height: 1.4,
  );

  static const TextStyle errorText = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.error500,
    height: 1.4,
  );

  static const TextStyle infoText = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.info500,
    height: 1.4,
  );

  // ===== SYSTEM SPECIFIC STYLES =====
  static const Map<String, TextStyle> systemTitleStyles = {
    'tarot': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.tarotPurple,
      letterSpacing: -0.2,
    ),
    'astrology': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.astrologyBlue,
      letterSpacing: -0.2,
    ),
    'numerology': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.numerologyGreen,
      letterSpacing: -0.2,
    ),
    'chinese': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.chineseRed,
      letterSpacing: -0.2,
    ),
    'runes': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.runesGold,
      letterSpacing: -0.2,
    ),
    'crystals': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.crystalPink,
      letterSpacing: -0.2,
    ),
    'ayurveda': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.ayurvedaOrange,
      letterSpacing: -0.2,
    ),
    'kabbalah': TextStyle(
      fontSize: 20,
      fontWeight: semiBold,
      color: AppColors.kabbalaWhite,
      letterSpacing: -0.2,
    ),
  };

  // ===== HELPER METHODS =====
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  static TextStyle withSize(TextStyle style, double fontSize) {
    return style.copyWith(fontSize: fontSize);
  }

  static TextStyle withWeight(TextStyle style, FontWeight fontWeight) {
    return style.copyWith(fontWeight: fontWeight);
  }

  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withValues(alpha: opacity));
  }

  static TextStyle withShadow(TextStyle style, Color shadowColor) {
    return style.copyWith(
      shadows: [
        Shadow(
          color: shadowColor,
          offset: const Offset(0, 2),
          blurRadius: 4,
        ),
      ],
    );
  }
}
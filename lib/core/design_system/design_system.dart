// Design System Exports
// This file provides a single entry point for all design system components

import 'package:flutter/material.dart';
import 'colors/app_colors.dart';

// Core Design Tokens
export 'colors/app_colors.dart';
export 'typography/app_text_styles.dart';
export 'spacing/app_spacing.dart';

// Basic Components
export 'components/app_button.dart';
export 'components/app_card.dart';
export 'components/app_input.dart';

// Mystical Components
export 'components/mystical_components.dart';

// Theme Configuration
export '../../config/themes/app_theme.dart';

/// Premium Oracle Design System
/// 
/// A comprehensive design system for the Premium Oracle mystical consultation app.
/// 
/// This design system includes:
/// - Color palette optimized for mystical/spiritual themes
/// - Typography system with decorative fonts for mystical content
/// - Spacing and sizing tokens following Material Design principles
/// - Reusable UI components for consistent user experience
/// - Specialized mystical components (Tarot cards, Runes, etc.)
/// 
/// Usage:
/// ```dart
/// import 'package:premium_oracle/core/design_system/design_system.dart';
/// 
/// // Use colors
/// Container(color: AppColors.mysticPurple500)
/// 
/// // Use text styles
/// Text('Title', style: AppTextStyles.headlineLarge)
/// 
/// // Use spacing
/// Padding(padding: AppSpacing.paddingMD)
/// 
/// // Use components
/// AppButton.primary(text: 'Get Reading', onPressed: () {})
/// ```
class DesignSystem {
  DesignSystem._();

  /// Design system version
  static const String version = '1.0.0';

  /// Design system name
  static const String name = 'Premium Oracle Design System';

  /// Color palette documentation
  static const String colorDocumentation = '''
  Color Palette:
  - Primary: Mystic Purple (#6A4C93) - Main brand color
  - Secondary: Cosmic Gold (#FFCB77) - Accent and highlights
  - Accent: Mystical Rose (#FF6B6B) - Error states and alerts
  - Background: Cosmic Dark (#1A1A2E) - Main background
  - Surface: Deep Blue (#16213E) - Card and component backgrounds
  - Card: Dark Blue (#0F3460) - Elevated card backgrounds
  
  Each color has a full range from 50 (lightest) to 900 (darkest).
  ''';

  /// Typography documentation
  static const String typographyDocumentation = '''
  Typography System:
  - Display: 48px, 40px, 32px - Hero titles and major headings
  - Headline: 28px, 24px, 20px - Section headings and page titles
  - Title: 18px, 16px, 14px - Component titles and labels
  - Body: 16px, 14px, 12px - Main content and descriptions
  - Label: 14px, 12px, 10px - Form labels and metadata
  - Mystic: Decorative fonts for mystical content
  - Mono: Monospace fonts for numbers and codes
  ''';

  /// Spacing documentation
  static const String spacingDocumentation = '''
  Spacing System (4px base unit):
  - XXS: 4px  - Minimal spacing
  - XS: 8px   - Small spacing
  - SM: 12px  - Small-medium spacing
  - MD: 16px  - Medium spacing (most common)
  - LG: 20px  - Large spacing
  - XL: 24px  - Extra large spacing
  - XXL: 32px - Double extra large spacing
  - XXXL: 40px - Triple extra large spacing
  ''';

  /// Component usage guide
  static const String componentGuide = '''
  Component Usage:
  
  Buttons:
  - AppButton.primary() - Main actions
  - AppButton.secondary() - Secondary actions
  - AppButton.mystic() - Special mystical actions
  - AppButton.ghost() - Subtle actions
  
  Cards:
  - AppCard.basic() - Simple content containers
  - AppCard.elevated() - Emphasized content
  - AppCard.mystic() - Mystical content with special styling
  - MysticalCard() - Pre-styled for mystical content
  - PredictionCard() - Specialized for predictions
  
  Inputs:
  - AppInput() - Basic text input
  - AppInput.email() - Email input with validation
  - AppInput.password() - Password input with visibility toggle
  - AppInput.search() - Search input with clear button
  
  Mystical Components:
  - TarotCard() - Tarot card display
  - SystemCard() - Mystical system selection
  - RuneSymbol() - Rune symbol display
  - CrystalCard() - Crystal information card
  - NumerologyNumber() - Number with meaning
  - AstrologySign() - Zodiac sign display
  ''';

  /// Best practices for using the design system
  static const String bestPractices = '''
  Best Practices:
  
  1. Always use design tokens instead of hardcoded values
  2. Prefer semantic colors over specific color values
  3. Use consistent spacing throughout the app
  4. Follow the component hierarchy and don't modify base components
  5. Use mystical components for spiritual/esoteric content
  6. Maintain proper contrast ratios for accessibility
  7. Test components in both light and dark themes
  8. Use proper text styles for content hierarchy
  9. Apply consistent border radius and shadows
  10. Leverage gradients sparingly for emphasis
  ''';

  /// Returns design system information
  static Map<String, dynamic> getSystemInfo() {
    return {
      'name': name,
      'version': version,
      'colorPalette': colorDocumentation,
      'typography': typographyDocumentation,
      'spacing': spacingDocumentation,
      'components': componentGuide,
      'bestPractices': bestPractices,
    };
  }

  /// Validates if a color is part of the design system
  static bool isValidColor(Color color) {
    // Add validation logic for design system colors
    return true; // Simplified for now
  }

  /// Validates if a text style follows design system guidelines
  static bool isValidTextStyle(TextStyle style) {
    // Add validation logic for text styles
    return true; // Simplified for now
  }

  /// Returns all available mystical system colors
  static Map<String, Color> getMysticalSystemColors() {
    return AppColors.mysticalSystemColors;
  }

  /// Returns color for a specific mystical system
  static Color? getSystemColor(String systemName) {
    return AppColors.mysticalSystemColors[systemName.toLowerCase()];
  }
}
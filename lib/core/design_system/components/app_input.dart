import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../colors/app_colors.dart';
import '../spacing/app_spacing.dart';
import '../typography/app_text_styles.dart';

enum AppInputType {
  text,
  email,
  password,
  number,
  phone,
  multiline,
  search,
}

enum AppInputSize {
  small,
  medium,
  large,
}

class AppInput extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final TextEditingController? controller;
  final AppInputType type;
  final AppInputSize size;
  final bool isRequired;
  final bool isEnabled;
  final bool isReadOnly;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final FormFieldValidator<String>? validator;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final int? maxLength;
  final EdgeInsets? contentPadding;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? borderColor;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;

  const AppInput({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.type = AppInputType.text,
    this.size = AppInputSize.medium,
    this.isRequired = false,
    this.isEnabled = true,
    this.isReadOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.inputFormatters,
    this.maxLines,
    this.maxLength,
    this.contentPadding,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.textInputAction,
    this.focusNode,
  });

  const AppInput.email({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.size = AppInputSize.medium,
    this.isRequired = false,
    this.isEnabled = true,
    this.isReadOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.contentPadding,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.textInputAction,
    this.focusNode,
  })  : type = AppInputType.email,
        inputFormatters = null,
        maxLines = 1,
        maxLength = null;

  const AppInput.password({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.size = AppInputSize.medium,
    this.isRequired = false,
    this.isEnabled = true,
    this.isReadOnly = false,
    this.prefixIcon,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.contentPadding,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.textInputAction,
    this.focusNode,
  })  : type = AppInputType.password,
        suffixIcon = null,
        inputFormatters = null,
        maxLines = 1,
        maxLength = null;

  const AppInput.search({
    super.key,
    this.hint,
    this.controller,
    this.size = AppInputSize.medium,
    this.isEnabled = true,
    this.onChanged,
    this.onSubmitted,
    this.contentPadding,
    this.borderRadius,
    this.backgroundColor,
    this.focusNode,
  })  : type = AppInputType.search,
        label = null,
        helperText = null,
        errorText = null,
        isRequired = false,
        isReadOnly = false,
        prefixIcon = null,
        suffixIcon = null,
        onTap = null,
        validator = null,
        inputFormatters = null,
        maxLines = 1,
        maxLength = null,
        borderColor = null,
        textInputAction = TextInputAction.search;

  @override
  State<AppInput> createState() => _AppInputState();
}

class _AppInputState extends State<AppInput> {
  late bool _isObscured;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _isObscured = widget.type == AppInputType.password;
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) _buildLabel(),
        _buildTextField(),
        if (widget.helperText != null || widget.errorText != null)
          _buildHelperText(),
      ],
    );
  }

  Widget _buildLabel() {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.xs),
      child: RichText(
        text: TextSpan(
          text: widget.label!,
          style: AppTextStyles.labelMedium,
          children: [
            if (widget.isRequired)
              const TextSpan(
                text: ' *',
                style: TextStyle(color: AppColors.error500),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField() {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? AppColors.cosmicDark700,
        borderRadius: widget.borderRadius ?? AppSpacing.inputRadius,
        border: Border.all(
          color: _getBorderColor(),
          width: _isFocused ? 2 : 1,
        ),
        boxShadow: _isFocused
            ? [
                BoxShadow(
                  color: AppColors.mysticPurple500.withValues(alpha: 0.2),
                  offset: const Offset(0, 0),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ]
            : null,
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        enabled: widget.isEnabled,
        readOnly: widget.isReadOnly,
        obscureText: _isObscured,
        keyboardType: _getKeyboardType(),
        textInputAction: widget.textInputAction ?? _getTextInputAction(),
        inputFormatters: widget.inputFormatters,
        maxLines: widget.maxLines ?? _getMaxLines(),
        maxLength: widget.maxLength,
        onTap: widget.onTap,
        onChanged: widget.onChanged,
        onFieldSubmitted: widget.onSubmitted,
        validator: widget.validator,
        style: _getTextStyle(),
        decoration: InputDecoration(
          hintText: widget.hint,
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textTertiary,
          ),
          prefixIcon: _buildPrefixIcon(),
          suffixIcon: _buildSuffixIcon(),
          contentPadding: widget.contentPadding ?? _getContentPadding(),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          counterText: '',
        ),
      ),
    );
  }

  Widget _buildHelperText() {
    return Padding(
      padding: const EdgeInsets.only(top: AppSpacing.xs),
      child: Text(
        widget.errorText ?? widget.helperText!,
        style: AppTextStyles.captionMedium.copyWith(
          color: widget.errorText != null
              ? AppColors.error500
              : AppColors.textTertiary,
        ),
      ),
    );
  }

  Widget? _buildPrefixIcon() {
    if (widget.type == AppInputType.search) {
      return const Icon(
        Icons.search,
        color: AppColors.textTertiary,
        size: AppSpacing.iconMD,
      );
    }

    if (widget.prefixIcon != null) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
        child: widget.prefixIcon!,
      );
    }

    return null;
  }

  Widget? _buildSuffixIcon() {
    if (widget.type == AppInputType.password) {
      return IconButton(
        onPressed: () {
          setState(() {
            _isObscured = !_isObscured;
          });
        },
        icon: Icon(
          _isObscured ? Icons.visibility_outlined : Icons.visibility_off_outlined,
          color: AppColors.textTertiary,
          size: AppSpacing.iconMD,
        ),
      );
    }

    if (widget.type == AppInputType.search && widget.controller?.text.isNotEmpty == true) {
      return IconButton(
        onPressed: () {
          widget.controller?.clear();
          widget.onChanged?.call('');
        },
        icon: const Icon(
          Icons.clear,
          color: AppColors.textTertiary,
          size: AppSpacing.iconSM,
        ),
      );
    }

    if (widget.suffixIcon != null) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
        child: widget.suffixIcon!,
      );
    }

    return null;
  }

  Color _getBorderColor() {
    if (widget.errorText != null) {
      return AppColors.error500;
    }

    if (_isFocused) {
      return AppColors.mysticPurple500;
    }

    if (widget.borderColor != null) {
      return widget.borderColor!;
    }

    return AppColors.borderPrimary;
  }

  TextInputType _getKeyboardType() {
    switch (widget.type) {
      case AppInputType.email:
        return TextInputType.emailAddress;
      case AppInputType.number:
        return TextInputType.number;
      case AppInputType.phone:
        return TextInputType.phone;
      case AppInputType.multiline:
        return TextInputType.multiline;
      default:
        return TextInputType.text;
    }
  }

  TextInputAction _getTextInputAction() {
    switch (widget.type) {
      case AppInputType.search:
        return TextInputAction.search;
      case AppInputType.multiline:
        return TextInputAction.newline;
      default:
        return TextInputAction.next;
    }
  }

  int _getMaxLines() {
    switch (widget.type) {
      case AppInputType.multiline:
        return 3;
      default:
        return 1;
    }
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case AppInputSize.small:
        return AppTextStyles.bodySmall.copyWith(
          color: AppColors.textPrimary,
        );
      case AppInputSize.medium:
        return AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textPrimary,
        );
      case AppInputSize.large:
        return AppTextStyles.bodyLarge.copyWith(
          color: AppColors.textPrimary,
        );
    }
  }

  EdgeInsets _getContentPadding() {
    switch (widget.size) {
      case AppInputSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        );
      case AppInputSize.medium:
        return AppSpacing.inputPadding;
      case AppInputSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppSpacing.lg,
          vertical: AppSpacing.md,
        );
    }
  }
}
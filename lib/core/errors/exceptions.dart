class ServerException implements Exception {
  final String message;
  
  const ServerException({required this.message});
  
  @override
  String toString() => 'ServerException: $message';
}

class CacheException implements Exception {
  final String message;
  
  const CacheException({required this.message});
  
  @override
  String toString() => 'CacheException: $message';
}

class NetworkException implements Exception {
  final String message;
  
  const NetworkException({required this.message});
  
  @override
  String toString() => 'NetworkException: $message';
}

class AuthenticationException implements Exception {
  final String message;
  
  const AuthenticationException({required this.message});
  
  @override
  String toString() => 'AuthenticationException: $message';
}

class SubscriptionException implements Exception {
  final String message;
  
  const SubscriptionException({required this.message});
  
  @override
  String toString() => 'SubscriptionException: $message';
}

class LocationException implements Exception {
  final String message;
  
  const LocationException({required this.message});
  
  @override
  String toString() => 'LocationException: $message';
}

class WeatherException implements Exception {
  final String message;
  
  const WeatherException({required this.message});
  
  @override
  String toString() => 'WeatherException: $message';
}

class AstrologyException implements Exception {
  final String message;
  
  const AstrologyException({required this.message});
  
  @override
  String toString() => 'AstrologyException: $message';
}

class TarotException implements Exception {
  final String message;
  
  const TarotException({required this.message});
  
  @override
  String toString() => 'TarotException: $message';
}

class NumerologyException implements Exception {
  final String message;
  
  const NumerologyException({required this.message});
  
  @override
  String toString() => 'NumerologyException: $message';
}

class ValidationException implements Exception {
  final String message;
  
  const ValidationException({required this.message});
  
  @override
  String toString() => 'ValidationException: $message';
}
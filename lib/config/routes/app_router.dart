import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/authentication/presentation/pages/login_page.dart';
import '../../features/authentication/presentation/pages/register_page.dart';
import '../../features/authentication/presentation/pages/onboarding_page.dart';
import '../../features/daily_prediction/presentation/pages/daily_prediction_page.dart';
import '../../features/user_profile/presentation/pages/profile_page.dart';
import '../../features/subscription/presentation/pages/subscription_page.dart';
import '../../features/tarot/presentation/pages/tarot_page.dart';
import '../../features/astrology/presentation/pages/astrology_page.dart';
import '../../features/numerology/presentation/pages/numerology_page.dart';
import '../../core/design_system/demo/design_system_showcase.dart';

class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: '/onboarding',
    routes: [
      // Authentication Routes
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      
      // Main App Routes
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const DailyPredictionPage(),
        routes: [
          GoRoute(
            path: 'profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: 'subscription',
            name: 'subscription',
            builder: (context, state) => const SubscriptionPage(),
          ),
        ],
      ),
      
      // Mystical Systems Routes
      GoRoute(
        path: '/tarot',
        name: 'tarot',
        builder: (context, state) => const TarotPage(),
      ),
      GoRoute(
        path: '/astrology',
        name: 'astrology',
        builder: (context, state) => const AstrologyPage(),
      ),
      GoRoute(
        path: '/numerology',
        name: 'numerology',
        builder: (context, state) => const NumerologyPage(),
      ),
      
      // Design System (for development)
      GoRoute(
        path: '/design-system',
        name: 'design-system',
        builder: (context, state) => const DesignSystemShowcase(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );

  static GoRouter get router => _router;
}
# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_ORGANIZATION_ID=your_openai_org_id

# Claude API Configuration (Alternative to OpenAI)
CLAUDE_API_KEY=your_claude_api_key

# Google APIs (for location services)
GOOGLE_PLACES_API_KEY=your_google_places_api_key
GOOGLE_GEOCODING_API_KEY=your_google_geocoding_api_key

# App Configuration
APP_ENV=development
DEBUG_MODE=true
LOG_LEVEL=debug

# Analytics (Optional)
FIREBASE_PROJECT_ID=your_firebase_project_id
MIXPANEL_TOKEN=your_mixpanel_token

# In-App Purchases
APPLE_SHARED_SECRET=your_apple_shared_secret
GOOGLE_PLAY_SERVICE_ACCOUNT=your_google_play_service_account.json